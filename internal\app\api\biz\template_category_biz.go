package biz

import (
	"chongli/component"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"chongli/pkg/utils"
	"github.com/gin-gonic/gin"
)

type TemplateCategoryBiz struct {
	log                  *logger.Logger
	templateCategoryRepo repo.TemplateCategoryRepo
	templateRepo         repo.TemplateAIRepo
}

func NewTemplateCategoryBiz(
	bootStrap *component.BootStrap,
	templateCategoryRepo repo.TemplateCategoryRepo,
	templateRepo repo.TemplateAIRepo,
) *TemplateCategoryBiz {
	return &TemplateCategoryBiz{
		log:                  bootStrap.Log,
		templateCategoryRepo: templateCategoryRepo,
		templateRepo:         templateRepo,
	}
}

// GetTemplateCategoryList 获取模板分类列表
func (s *TemplateCategoryBiz) GetTemplateCategoryList(version string, mainClassId int64) ([]*dto.TemplateCategoryDto, errpkg.IError) {
	// 调用数据层获取分类列表，只查询启用且未删除的分类
	categories, err := s.templateCategoryRepo.ListTemplateCategory(version, mainClassId)
	if err != nil {
		s.log.Error("获取模板分类列表失败: %v", err)
		return nil, errpkg.NewHighError(response.DbError)
	}

	return categories, nil
}

func (s *TemplateCategoryBiz) GetCategoryWithTemplateList(ctx *gin.Context, version string, mainClassId int64) ([]*dto.CategoryWithTemplateResp, errpkg.IError) {
	maxVersionInt := utils.VersionToVersionInt(version)
	if maxVersionInt <= 0 {
		return nil, errpkg.NewMiddleError("invalid version")
	}

	var resp []*dto.CategoryWithTemplateResp
	var categoryTemplateMap = make(map[int64][]*dto.TemplateAIDTO)

	categories, err := s.templateCategoryRepo.ListTemplateCategory(version, mainClassId)
	if err != nil {
		s.log.Error("获取模板分类列表失败: %v", err)
		return nil, errpkg.NewHighError(response.DbError)
	}

	for _, category := range categories {
		templates, err := s.templateRepo.ListByQuery(ctx, &dto.TemplateAIQueryDTO{
			Template: dto.TemplateAIDTO{
				CategoryID:    uint64(category.ID),
				MaxVersionInt: maxVersionInt,
			},
			PageNum:        1,
			PageSize:       10,
			OrderBy:        "sort_order",
			OrderDirection: "asc",
		})
		if err != nil {
			s.log.Error("获取模板列表失败: %v", err)
			return nil, errpkg.NewHighError(response.DbError)
		}
		categoryTemplateMap[category.ID] = templates
	}

	resp = make([]*dto.CategoryWithTemplateResp, len(categories))
	for i, category := range categories {
		resp[i] = &dto.CategoryWithTemplateResp{
			Category:  category,
			Templates: s.convertTemplateAIDTO2Dto(categoryTemplateMap[category.ID]),
		}
	}

	return resp, nil
}

func (s *TemplateCategoryBiz) convertTemplateAIDTO2Dto(data []*dto.TemplateAIDTO) []*dto.TemplateDto {
	var resp []*dto.TemplateDto
	for _, template := range data {
		resp = append(resp, &dto.TemplateDto{
			ID:              template.ID,
			Name:            template.Name,
			CoverURL:        utils.EnsureHttpsPrefix(template.CoverURL),
			VideoCoverURL:   utils.EnsureHttpsPrefix(template.VideoCoverURL),
			VideoFirstFrame: utils.EnsureHttpsPrefix(template.VideoFirstFrame),
			Status:          template.Status,
			SortOrder:       template.SortOrder,
			MaxVersion:      template.MaxVersion,
			CategoryID:      template.CategoryID,
			CreateAt:        template.CreateAt,
			UpdatedAt:       template.UpdatedAt,
			DiamondCost:     template.DiamondCost,
			MainClass:       template.MainClass,
			MakeType:        template.MakeType,
			VideoHigh:       template.VideoHigh,
			VideoWidth:      template.VideoWidth,
			ImageHigh:       template.ImageHigh,
			ImageWidth:      template.ImageWidth,
		})
	}
	return resp
}
