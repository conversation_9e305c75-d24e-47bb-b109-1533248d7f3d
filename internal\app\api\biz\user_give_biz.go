package biz

import (
	"chongli/component"
	"chongli/component/driver"
	"chongli/internal/model"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	resp "chongli/pkg/response"
	"context"
	"strconv"
	"time"
)

type UserGiveBiz struct {
	log           *logger.Logger
	tx            driver.ITransaction
	redisRepo     repo.RedisRepo
	userService   *service.UserService
	configService *service.ConfigService
}

func NewUserGiveBiz(
	bootStrap *component.BootStrap,
	redisRepo repo.RedisRepo,
	userService *service.UserService,
	configService *service.ConfigService,
) *UserGiveBiz {
	return &UserGiveBiz{
		log:           bootStrap.Log,
		tx:            bootStrap.Tx,
		redisRepo:     redisRepo,
		userService:   userService,
		configService: configService,
	}
}

// GiveUserEveryDayFreeDiamond 赠送用户每日免费钻石
func (b *UserGiveBiz) GiveUserEveryDayFreeDiamond(c context.Context, req map[string]any) errpkg.IError {
	userId := req["userId"].(int64)
	deviceId := req["deviceId"].(string)
	version := req["version"].(string)
	channel := req["channel"].(string)

	// 添加完整的 panic 恢复机制
	defer func() {
		if r := recover(); r != nil {
			b.log.Error("GiveUserEveryDayFreeDiamond panic: %v, userId: %d", r, userId)
		}
	}()

	if userId <= 0 {
		b.log.Error("无效的用户ID: %d", userId)
		return errpkg.NewLowError("无效的用户ID")
	}

	today := time.Now().Format("2006-01-02")
	userIdStr := strconv.Itoa(int(userId))

	// 先检查Redis缓存，避免重复处理
	isExist, err := b.redisRepo.Exists(c, "chongli:user:"+userIdStr+":"+today)
	if err != nil {
		b.log.Error("获取 Redis 键失败: %v", err)
		return errpkg.NewHighError(resp.RedisError)
	}

	if isExist {
		b.log.Info("用户[%d]今天已领取过免费钻石", userId)
		return errpkg.NewLowError("今天已领取过免费钻石")
	}

	// 使用分布式锁防止并发问题
	lockKey := "chongli:lock:user_diamond:" + userIdStr + ":" + today
	lockValue := strconv.FormatInt(time.Now().UnixNano(), 10)

	// 尝试获取锁，使用SET NX EX命令确保原子性
	lockAcquired := false
	if err := b.redisRepo.SetNX(c, lockKey, lockValue, 30*time.Second); err == nil {
		lockAcquired = true
		defer func() {
			// 释放锁，使用Lua脚本确保原子性
			if lockAcquired {
				_ = b.redisRepo.DelLock(c, lockKey, lockValue)
			}
		}()
	} else {
		b.log.Warning("获取分布式锁失败，用户[%d]可能正在被其他进程处理: %v", userId, err)
		return errpkg.NewLowError("正在处理中，请稍后再试")
	}

	// 再次检查是否已处理（双重检查）
	isExist, err = b.redisRepo.Exists(c, "chongli:user:"+userIdStr+":"+today)
	if err != nil {
		b.log.Error("二次检查 Redis 键失败: %v", err)
		return errpkg.NewHighError(resp.RedisError)
	}

	if isExist {
		b.log.Info("用户[%d]今天已领取过免费钻石（二次检查）", userId)
		return errpkg.NewLowError("今天已领取过免费钻石")
	}

	b.log.Info("开始赠送用户[%d]今天的免费钻石", userId)

	// 创建数据库事务
	mysqlTx := b.tx.MysqlDbTxBegin()
	defer func() {
		if mysqlTx != nil {
			mysqlTx.Rollback()
		}
	}()

	// 获取配置
	diamondStr, err := b.configService.GetConfigByKeyFromCache(c, "every_day_give_diamond")
	if err != nil {
		b.log.Error("获取配置失败: %v", err)
		return errpkg.NewHighError(resp.DbError)
	}

	diamondNum, err := strconv.Atoi(diamondStr)
	if err != nil {
		b.log.Error("转换免费钻石数量失败: %v", err)
		return errpkg.NewLowError("配置错误")
	}

	// 赠送钻石
	if err := b.userService.AddOrSubUserDiamondAndAddRecord(service.DiamondOperationRequest{
		UserID:  userId,
		Diamond: uint64(diamondNum),
		Op:      int(model.StatusEnabled),
		Mark:    "赠送每日免费钻石",
		Version: version,
		Channel: channel,
	}, mysqlTx); err != nil {
		b.log.Error("赠送用户[%d]免费钻石失败: %v", userId, err)
		return err
	}

	// 添加领取记录
	if err := b.userService.CreateUserReceiveRecord(&dto.CreateUserReceiveRequest{
		UserID:     userId,
		DeviceID:   deviceId,
		ReceiveNum: diamondNum,
		Version:    version,
		Channel:    channel,
	}, mysqlTx); err != nil {
		b.log.Error("创建用户领取记录失败: %v", err)
		return errpkg.NewMiddleError(err.Error())
	}

	// 在事务提交前设置 Redis 键，确保原子性
	// 如果 Redis 设置失败，整个事务都会回滚，避免重复赠送
	if err := b.redisRepo.Set(c, "chongli:user:"+userIdStr+":"+today, "1", 24*time.Hour); err != nil {
		b.log.Error("设置 Redis 键失败: %v", err)
		return errpkg.NewHighError(resp.RedisError)
	}

	// 提交事务
	if err := mysqlTx.Commit().Error; err != nil {
		b.log.Error("事务提交失败: %v", err)
		// 事务提交失败，需要清理已设置的 Redis 键
		_ = b.redisRepo.Del(c, "chongli:user:"+userIdStr+":"+today)
		return errpkg.NewHighError(resp.DbError)
	}

	// 标记事务已提交，避免重复回滚
	mysqlTx = nil

	b.log.Info("用户[%d]领取免费钻石成功", userId)
	return nil
}
