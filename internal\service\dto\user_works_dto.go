package dto

import (
	"chongli/internal/model"
	"time"
)

type UserWorks struct {
	ID                 uint64           `json:"id"`
	UserID             uint64           `json:"user_id"`
	WorkType           string           `json:"work_type"`
	Cover              string           `json:"cover"`
	TemplateID         uint64           `json:"template_id"`
	Status             model.StatusFlag `json:"status"`
	ErrMsg             string           `json:"error_msg"`
	CreateAt           time.Time        `json:"create_at"`
	UpdatedAt          time.Time        `json:"update_at"`
	DeletedAt          *time.Time       `json:"deleted_at"`
	IsDeleted          model.StatusFlag `json:"is_deleted"`
	ExpectedFinishTime *time.Time       `json:"expected_finish_time"`
	PicURL             string           `json:"pic_url"`
	VideoURL           string           `json:"video_url"`
	VideoFirstFrame    string           `json:"video_first_frame"`
	Diamond            int64            `json:"diamond"`
	VideoHigh          int64            `json:"video_high"`  // 视频分辨率高
	VideoWidth         int64            `json:"video_width"` // 视频分辨率宽
	ImageHigh          int64            `json:"image_high"`  // 图片分辨率高
	ImageWidth         int64            `json:"image_width"` // 图片分辨率宽

	// 分页相关字段（不会存储到数据库）
	Page     int          `json:"page,omitempty" gorm:"-"`      // 页码
	PageSize int          `json:"page_size,omitempty" gorm:"-"` // 每页数量
	Total    int64        `json:"total,omitempty" gorm:"-"`     // 总记录数
	User     *UserInfoDto `json:"user,omitempty" gorm:"-"`      // 用户信息
	Template *TemplateDto `json:"template,omitempty" gorm:"-"`  // 模板信息
}
