package model

import "time"

type UserReceive struct {
	ID         int64     `gorm:"column:id;primary_key;AUTO_INCREMENT"`     // 主键id
	UserID     int64     `gorm:"column:user_id;default:0;NOT NULL"`        // 用户id
	DeviceID   string    `gorm:"column:device_id;default:'';NOT NULL"`     // 设备id
	ReceiveNum int       `gorm:"column:receive_num;default:0;NOT NULL"`    // 领取数量
	ReceiveAt  time.Time `gorm:"column:receive_at;NOT NULL"`               // 领取时间
	Version    string    `gorm:"column:version;default:'';NOT NULL"`       // APP版本
	Channel    string    `gorm:"column:channel;default:'';NOT NULL"`       // 设备渠道
	CreateAt   time.Time `gorm:"column:create_at;NOT NULL;autoCreateTime"` // 创建时间
}

func (*UserReceive) TableName() string {
	return "user_receive"
}
