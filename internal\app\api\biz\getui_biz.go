package biz

import (
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/response"
)

type GeTuiBiz struct {
	userBindGetuiRepo repo.UserBindGetuiRepo
}

func NewGeTuiBiz(userBindGetuiRepo repo.UserBindGetuiRepo) *GeTuiBiz {
	return &GeTuiBiz{
		userBindGetuiRepo: userBindGetuiRepo,
	}
}

func (b *GeTuiBiz) BindGeTui(req *dto.BindGeTuiCidRequest) (string, errpkg.IError) {
	// 先判断是否已经有绑定的cid
	cid, err := b.userBindGetuiRepo.GetBindInfoByUserId(req.UserId)
	if err != nil {
		return "", errpkg.NewHighError(response.DbError)
	}
	if cid.ID != 0 {
		// 更新绑定
		if err := b.userBindGetuiRepo.UpdateBind(req.UserId, req.ClientID); err != nil {
			return "", errpkg.NewHighError(response.DbError)
		}
		return "OK", nil
	}

	// 再判断 cid 是否已存在
	hasCid, err := b.userBindGetuiRepo.GetBindInfoByCid(req.ClientID)
	if err != nil {
		return "", errpkg.NewHighError(response.DbError)
	}
	// 如果已存在绑定，删除原来的cid绑定
	if hasCid.ID != 0 {
		if err := b.userBindGetuiRepo.DeleteBindByUserId(int64(hasCid.UserID)); err != nil {
			return "", errpkg.NewHighError(response.DbError)
		}
	}

	// 绑定
	if err := b.userBindGetuiRepo.BindUserIdAndCid(req.UserId, req.ClientID); err != nil {
		return "", errpkg.NewHighError(response.DbError)
	}
	return "OK", nil
}
