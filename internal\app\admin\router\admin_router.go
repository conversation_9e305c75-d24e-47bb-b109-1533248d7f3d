package router

import (
	"chongli/internal/app/admin/controller"
	"chongli/internal/middleware"

	"github.com/gin-gonic/gin"
)

type AdminRouter struct {
	authCtrl                  *controller.AuthController
	accountCtrl               *controller.AccountController
	channelCtrl               *controller.ChannelController
	configCtrl                *controller.ConfigController
	popupCtrl                 *controller.PopupController
	commonCtrl                *controller.CommonController
	channelPopupCtrl          *controller.ChannelPopupController
	configChannelRelationCtrl *controller.ConfigChannelRelationController
	versionCtrl               *controller.VersionController
	bannerCtrl                *controller.BannerController
}

func NewAdminRouter(engine *gin.Engine,
	authCtrl *controller.AuthController,
	accountCtrl *controller.AccountController,
	channelCtrl *controller.ChannelController,
	configCtrl *controller.ConfigController,
	popupCtrl *controller.PopupController,
	commonCtrl *controller.CommonController,
	channelPopupCtrl *controller.ChannelPopupController,
	configChannelRelationCtrl *controller.ConfigChannelRelationController,
	versionCtrl *controller.VersionController,
	bannerCtrl *controller.BannerController,
) *AdminRouter {
	router := &AdminRouter{
		authCtrl:                  authCtrl,
		accountCtrl:               accountCtrl,
		channelCtrl:               channelCtrl,
		configCtrl:                configCtrl,
		popupCtrl:                 popupCtrl,
		commonCtrl:                commonCtrl,
		channelPopupCtrl:          channelPopupCtrl,
		configChannelRelationCtrl: configChannelRelationCtrl,
		versionCtrl:               versionCtrl,
		bannerCtrl:                bannerCtrl,
	}

	// 管理后台认证相关路由（无需鉴权）
	auth := engine.Group("/admin/auth")
	{
		auth.POST("/send-code", router.authCtrl.SendVerifyCode) // 发送邮箱验证码
		auth.POST("/verify-code", router.authCtrl.VerifyCode)   // 验证邮箱验证码并登录
	}

	// 管理员用户相关路由（需要JWT验证）
	account := engine.Group("/admin/account")
	account.Use(middleware.AdminJWTAuth()) // 添加JWT中间件
	{
		account.GET("/list", router.accountCtrl.AccountList)         // 更新当前用户信息
		account.POST("/edit/:id", router.accountCtrl.AccountAdd)     // 更新当前用户信息
		account.POST("/add", router.accountCtrl.AccountAdd)          // 更新当前用户信息
		account.GET("/delete/:id", router.accountCtrl.AccountDelete) // 更新当前用户信息
	}
	// 营销渠道相关路由（需要JWT验证）
	channel := engine.Group("/admin/channel")
	channel.Use(middleware.AdminJWTAuth()) // 添加JWT中间件
	{
		channel.GET("/list", router.channelCtrl.ChannelList)            // 获取渠道列表
		channel.GET("/detail/:id", router.channelCtrl.ChannelDetail)    // 获取渠道详情
		channel.POST("/add", router.channelCtrl.ChannelAdd)             // 创建渠道
		channel.POST("/update", router.channelCtrl.ChannelUpdate)       // 更新渠道
		channel.DELETE("/delete/:id", router.channelCtrl.ChannelDelete) // 删除渠道
		channel.GET("/count", router.channelCtrl.ChannelCount)          // 获取渠道总数
	}

	// 配置相关路由（需要JWT验证）
	config := engine.Group("/admin/config")
	config.Use(middleware.AdminJWTAuth()) // 添加JWT中间件
	{
		config.GET("/list", router.configCtrl.ConfigList)         // 获取配置列表
		config.GET("/detail/:id", router.configCtrl.ConfigDetail) // 获取配置详情
		config.POST("/add", router.configCtrl.ConfigAdd)          // 创建配置
		config.POST("/update", router.configCtrl.ConfigUpdate)    // 更新配置
		config.POST("/delete", router.configCtrl.ConfigDelete)    // 删除配置
	}
	// 弹窗相关路由（需要JWT验证）
	popup := engine.Group("/admin/popup")
	popup.Use(middleware.AdminJWTAuth()) // 添加JWT中间件
	{
		popup.GET("/list", router.popupCtrl.PopupList)                        // 获取弹窗列表
		popup.GET("/detail/:id", router.popupCtrl.PopupDetail)                // 获取弹窗详情
		popup.POST("/add", router.popupCtrl.PopupAdd)                         // 创建弹窗
		popup.POST("/update", router.popupCtrl.PopupUpdate)                   // 更新弹窗
		popup.DELETE("/delete/:id", router.popupCtrl.PopupDelete)             // 删除弹窗
		popup.GET("/location/:location", router.popupCtrl.GetPopupByLocation) // 根据位置获取弹窗
	}

	// 通用上传相关路由（需要JWT验证）
	common := engine.Group("/admin/common")
	common.Use(middleware.AdminJWTAuth()) // 添加JWT中间件
	{
		common.POST("/upload/img", router.commonCtrl.UploadImg)     // 上传图片
		common.POST("/upload/file", router.commonCtrl.UploadFile)   // 上传文件
		common.POST("/upload/video", router.commonCtrl.UploadVideo) // 上传文件
	}

	// 渠道弹窗关联相关路由（需要JWT验证）
	channelPopup := engine.Group("/admin/channel-popup")
	channelPopup.Use(middleware.AdminJWTAuth()) // 添加JWT中间件
	{
		channelPopup.GET("/list", router.channelPopupCtrl.ChannelPopupList)                 // 获取渠道弹窗关联列表（通用查询）
		channelPopup.POST("/add", router.channelPopupCtrl.ChannelPopupAdd)                  // 创建渠道弹窗关联
		channelPopup.DELETE("/delete/:id", router.channelPopupCtrl.ChannelPopupDelete)      // 删除渠道弹窗关联
		channelPopup.POST("/batch-delete", router.channelPopupCtrl.ChannelPopupBatchDelete) // 批量删除关联
	}

	// 配置渠道关联相关路由（需要JWT验证）
	configChannel := engine.Group("/admin/config-channel")
	configChannel.Use(middleware.AdminJWTAuth()) // 添加JWT中间件
	{
		configChannel.GET("/list", router.configChannelRelationCtrl.ConfigChannelRelationList)                 // 获取配置渠道关联列表（通用查询）
		configChannel.POST("/add", router.configChannelRelationCtrl.ConfigChannelRelationAdd)                  // 批量创建配置渠道关联
		configChannel.POST("/delete", router.configChannelRelationCtrl.ConfigChannelRelationDelete)            // 删除配置渠道关联
		configChannel.POST("/batch-delete", router.configChannelRelationCtrl.ConfigChannelRelationBatchDelete) // 批量删除配置渠道关联
	}

	version := engine.Group("/admin/version")
	version.Use(middleware.AdminJWTAuth()) // 添加JWT中间件
	{
		version.GET("/list", router.versionCtrl.GetList)                // 获取版本列表
		version.GET("/detail/:id", router.versionCtrl.GetVersionDetail) // 获取版本详情
		version.POST("/add", router.versionCtrl.AddVersion)             // 创建版本
		version.POST("/update", router.versionCtrl.UpdateVersion)       // 更新版本
		version.GET("/release", router.versionCtrl.ReleaseVersion)      // 发布/取消发布版本（跳跃状态）
		version.DELETE("/delete/:id", router.versionCtrl.DeleteVersion) // 删除版本
	}

	// Banner管理相关路由（需要JWT验证）
	banner := engine.Group("/admin/banner")
	banner.Use(middleware.AdminJWTAuth()) // 添加JWT中间件
	{
		banner.GET("/list", router.bannerCtrl.GetBannerList)              // 获取Banner列表
		banner.GET("/detail/:id", router.bannerCtrl.GetBannerDetail)      // 获取Banner详情
		banner.POST("/create", router.bannerCtrl.CreateBanner)            // 创建Banner
		banner.POST("/update", router.bannerCtrl.UpdateBanner)            // 更新Banner
		banner.POST("/batch-update", router.bannerCtrl.BatchUpdateBanner) // 批量更新Banner
		banner.GET("/all_location", router.bannerCtrl.GetAllLocation)     // 获取所有位置标识
	}

	return router
}
