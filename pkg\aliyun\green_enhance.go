package aliyun

import (
	"chongli/component"
	"chongli/component/apollo"
	"chongli/pkg/logger"
	"chongli/pkg/utils"
	"encoding/json"
	"errors"
	"fmt"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	green20220302 "github.com/alibabacloud-go/green-20220302/v2/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"strings"
)

const (
	// EndPointPeking 华北2(北京)
	EndPointPeking = `green-cip.cn-beijing.aliyuncs.com`
)

type AliGreenClient struct {
	// greenClient 客户端
	greenClient *green20220302.Client
	log         *logger.Logger
	config      *apollo.Config
}

// NewAliGreenClient 创建阿里云检测客户端
func NewAliGreenClient(bootStrap *component.BootStrap) *AliGreenClient {
	var err error
	aliGreenClient := &AliGreenClient{
		log:         bootStrap.Log,
		config:      bootStrap.Config,
		greenClient: &green20220302.Client{},
	}

	// 使用AccessKey、AccessKeySecret 初始化账号Client.
	config := &openapi.Config{
		AccessKeyId:     tea.String(aliGreenClient.config.GreenAccessKeyID),
		AccessKeySecret: tea.String(aliGreenClient.config.GreenAccessKeySecret),
		Endpoint:        tea.String(EndPointPeking),
	}

	aliGreenClient.greenClient, err = green20220302.NewClient(config)
	if err != nil {
		aliGreenClient.log.Error("创建阿里云检测客户端失败: %v", err)
	}

	return aliGreenClient
}

var ContentRiskDescriptions = map[string]string{
	// 色情相关风险
	"pornographic_adultContent":     "疑似含有成人色情内容。",
	"pornographic_adultContent_tii": "图中文字疑似含有色情内容。",
	"sexual_suggestiveContent":      "疑似含有疑似低俗或性暗示内容。",
	"sexual_partialNudity":          "疑似含有包含肢体裸露或性感内容。",

	// 政治相关风险
	"political_politicalFigure":              "疑似包含政治敏感，无法生成请修改后再试",
	"political_politicalFigure_name_tii":     "疑似包含政治敏感，无法生成请修改后再试",
	"political_politicalFigure_metaphor_tii": "疑似包含政治敏感，无法生成请修改后再试",
	"political_TVLogo":                       "疑似包含政治敏感，无法生成请修改后再试",
	"political_map":                          "疑似包含政治敏感，无法生成请修改后再试",
	"political_outfit":                       "疑似包含政治敏感，无法生成请修改后再试",
	"political_prohibitedPerson":             "疑似包含政治敏感，无法生成请修改后再试",
	"political_prohibitedPerson_tii":         "疑似包含政治敏感，无法生成请修改后再试",
	"political_taintedCelebrity":             "疑似包含政治敏感，无法生成请修改后再试",
	"political_taintedCelebrity_tii":         "疑似包含政治敏感，无法生成请修改后再试",
	"political_flag":                         "疑似包含政治敏感，无法生成请修改后再试",
	"political_historicalNihility":           "疑似包含政治敏感，无法生成请修改后再试",
	"political_historicalNihility_tii":       "疑似包含政治敏感，无法生成请修改后再试",
	"political_religion_tii":                 "疑似包含政治敏感，无法生成请修改后再试",
	"political_racism_tii":                   "疑似包含政治敏感，无法生成请修改后再试",
	"political_badge":                        "疑似包含政治敏感，无法生成请修改后再试",

	// 暴力相关风险
	"violent_explosion":       "疑似含有烟火类内容元素。",
	"violent_gunKnives":       "疑似含有刀具、枪支等内容。",
	"violent_gunKnives_tii":   "图中文字疑似含枪支刀具的描述。",
	"violent_armedForces":     "疑似含有武装元素。",
	"violent_crowding":        "疑似包含政治敏感，无法生成请修改后再试",
	"violent_horrificContent": "疑似含有惊悚、血腥等内容。",
	"violent_horrific_tii":    "图中文字疑似描述暴力、恐怖的内容。",

	// 违禁品相关风险
	"contraband_drug":       "含有疑似毒品等内容。",
	"contraband_drug_tii":   "图中文字疑似描述违禁毒品。",
	"contraband_gamble":     "含有疑似赌博等内容。",
	"contraband_gamble_tii": "图中文字疑似描述赌博行为。",

	// 欺诈相关风险
	"fraud_videoAbuse":  "图片疑似有隐藏视频风险。",
	"fraud_playerAbuse": "图片疑似有隐藏播放器风险。",

	"pornographic_adultToys":      "画面疑似含有成人器具内容。",
	"pornographic_cartoon":        "画面疑似含有卡通色情内容。",
	"pornographic_art":            "画面疑似含有艺术品色情内容。",
	"pornographic_suggestive_tii": "图中文字含低俗内容。",
	"pornographic_o_tii":          "图中文字含LGBT类内容。",
	"pornographic_organs_tii":     "图中文字含性器官描述内容。",
	"pornographic_adultToys_tii":  "图中文字含成人玩具类内容。",
	"sexual_femaleUnderwear":      "画面疑似含有内衣泳衣内容。",
	"sexual_cleavage":             "画面疑似含有女性乳沟特征。",
	"sexual_maleTopless":          "画面疑似含有男性赤膊内容。",
	"sexual_cartoon":              "画面疑似含有动漫类性感内容。",
	"sexual_shoulder":             "画面疑似含有肩部性感内容。",
	"sexual_femaleLeg":            "画面疑似含有腿部性感内容。",
	"sexual_pregnancy":            "画面疑似含有孕照哺乳内容。",
	"sexual_kiss":                 "画面疑似含有亲吻内容。",
	"sexual_intimacy":             "画面疑似含有亲密行为内容。",
	"sexual_intimacyCartoon":      "画面疑似含有卡通动漫亲密动作。",

	"political_prohibitedPerson_1": "画面疑似含有国家级落马官员。",
	"political_prohibitedPerson_2": "画面疑似含有省市级落马官员。",
	"political_CNFlag":             "画面疑似含有中国国旗。",
	"political_CNMap":              "画面疑似含有中国地图。",
	"political_logo":               "画面疑似含有禁宣媒体标识。",

	"violent_weapon":                "画面疑似包含军器装备。",
	"violent_gun":                   "画面疑似包含枪支。",
	"violent_knives":                "画面疑似包含刀具。",
	"violent_horrific":              "画面疑似包含惊悚内容。",
	"violent_nazi":                  "画面疑似包含特殊内容。",
	"violent_blood":                 "画面疑似包含血腥内容。",
	"violent_extremistGroups_tii":   "图中文字含暴恐组织内容。",
	"violent_extremistIncident_tii": "图中文字含暴恐事件内容。",
	"violence_weapons_tii":          "图中文字疑似含枪支刀具的描述。",
	"violent_ACU":                   "画面疑似包含作战服。",

	"flag_country":               "疑似包含政治敏感，无法生成请修改后再试",
	"inappropriate_smoking":      "画面疑似含有烟相关内容。",
	"inappropriate_drinking":     "画面疑似含有酒相关内容。",
	"inappropriate_tattoo":       "画面疑似含有纹身内容。",
	"inappropriate_middleFinger": "画面疑似含有竖中指内容。",
	"inappropriate_foodWasting":  "画面疑似含有浪费粮食内容。",
	"profanity_offensive_tii":    "图中文字疑似含有较严重辱骂，言语攻击等内容。",
	"profanity_oral_tii":         "图中文字疑似含有口头语性质的辱骂。",

	"political_politicalFigure_1": "疑似包含政治敏感，无法生成请修改后再试",
	"political_politicalFigure_2": "疑似包含政治敏感，无法生成请修改后再试",
	"political_politicalFigure_3": "疑似包含政治敏感，无法生成请修改后再试",
	"political_politicalFigure_4": "疑似包含政治敏感，无法生成请修改后再试",
	"religion_clothing":           "图中文字疑似含特定元素或者信息",
	"religion_logo":               "图中文字疑似含特定元素或者信息",
	"religion_flag":               "图中文字疑似含特定元素或者信息",
	"religion_taboo1_tii":         "图中文字疑似含特定元素或者信息",
	"religion_taboo2_tii":         "图中文字疑似含特定元素或者信息",
}

func (a *AliGreenClient) CheckImage(url string) (result string, err error) {
	url = utils.EnsureHttpsPrefix(url)
	resp, _err := a.imageDetection(url)
	if _err != nil {
		logger.Log().Error("CheckImage error: %v", _err)
		return "", _err
	}
	if resp == nil {
		// 记录一个警告日志，方便排查问题
		logger.Log().Info("CheckImage warning: nil response without error for url: %s", url)
		return "", nil
	}
	if resp.Body.Code != nil && *resp.Body.Code != 200 {
		logger.Log().Error("CheckImage error: response body is nil")
		return "", fmt.Errorf("图片检测响应体为空")
	}
	// 检查 resp.Body 和 resp.Body.Data 是否为 nil
	if resp.Body == nil || resp.Body.Data == nil {
		logger.Log().Error("CheckImage error: response body or data is nil")
		return "", fmt.Errorf("图片检测响应体为空")
	}
	num := float32(0)
	label := ""

	for _, v := range resp.Body.Data.Result {
		if v.Confidence == nil {
			continue
		}
		if *v.Confidence > num {
			num = *v.Confidence
			label = *v.Label
		}
	}
	if len(label) == 0 {
		return "", nil
	}
	result, exists := ContentRiskDescriptions[label]
	if !exists && label != "" {
		result = "未知风险内容"
	}
	return result, nil
}

// imageDetection 图片检测增强版
//
// Doc FYI(params of request or response) - https://next.api.aliyun.com/api/Green/2022-03-02/ImageModeration
func (a *AliGreenClient) imageDetection(imageURL string) (response *green20220302.ImageModerationResponse, err error) {
	imageModerationRequest := &green20220302.ImageModerationRequest{}
	serviceParameters := make(map[string]string)
	// 设置待检测图片URL.
	serviceParameters["imageUrl"] = imageURL

	serviceParametersBytes, _err := json.Marshal(serviceParameters)
	if _err != nil {
		return nil, _err
	}

	imageModerationRequest.SetService(a.config.ImageServiceName)
	imageModerationRequest.SetServiceParameters(string(serviceParametersBytes))

	runtime := &util.RuntimeOptions{}
	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		response, err = a.greenClient.ImageModerationWithOptions(imageModerationRequest, runtime)
		if err != nil {
			return err
		}

		return nil
	}()

	if tryErr != nil {
		var _err = &tea.SDKError{}
		var _t *tea.SDKError
		if errors.As(tryErr, &_t) {
			_err = _t
		} else {
			_err.Message = tea.String(tryErr.Error())
		}
		// 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
		// 错误 message
		fmt.Println(tea.StringValue(_err.Message))
		// 诊断地址
		var data any
		d := json.NewDecoder(strings.NewReader(tea.StringValue(_err.Data)))
		_ = d.Decode(&data)
		if m, ok := data.(map[string]any); ok {
			recommend, _ := m["Recommend"]
			fmt.Println(recommend)
		}
		_, err = util.AssertAsString(_err.Message)
		if err != nil {
			return nil, err
		}
	}

	return
}
