package controller

import (
	"chongli/component"
	"chongli/internal/app/api/biz"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/pkg/aliyun"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"chongli/pkg/utils"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
)

type UserWorksController struct {
	userWorkBiz    *biz.UserWorkBiz
	log            *logger.Logger
	aliGreenClient *aliyun.AliGreenClient
}

func NewUserWorksController(
	bootstrap *component.BootStrap,
	userWorkBiz *biz.UserWorkBiz,
	aliGreenClient *aliyun.AliGreenClient,
) *UserWorksController {
	return &UserWorksController{
		log:            bootstrap.Log,
		userWorkBiz:    userWorkBiz,
		aliGreenClient: aliGreenClient,
	}
}

// formatExpectedTime 格式化预期完成时间显示
func formatExpectedTime(expectedTime time.Time) string {
	now := time.Now()
	if expectedTime.After(now) {
		duration := expectedTime.Sub(now)
		totalMinutes := int(duration.Minutes())

		if totalMinutes >= 60 {
			hours := totalMinutes / 60
			minutes := totalMinutes % 60
			if minutes > 0 {
				return fmt.Sprintf("预计%d小时%d分钟", hours, minutes)
			} else {
				return fmt.Sprintf("预计%d小时", hours)
			}
		} else if totalMinutes > 0 {
			return fmt.Sprintf("预计%d分钟", totalMinutes)
		} else {
			return "即将完成"
		}
	} else {
		return "即将完成"
	}
}

// UserWorksListRequest 用户作品列表请求
type UserWorksListRequest struct {
	Page     int    `form:"page" binding:"required,min=1"`
	WorkType string `form:"work_type"` // 可选：pic, dance, sing
	UserID   int64  `json:"user_id"`   // 由中间件注入
}

// UserWorksListResponse 用户作品列表响应
type UserWorksListResponse struct {
	ID              uint64 `json:"id"`
	UserID          uint64 `json:"user_id"`
	WorkType        string `json:"work_type"`
	Cover           string `json:"cover"`
	TemplateID      uint64 `json:"template_id"`
	TemplateName    string `json:"template_name"`
	Status          int8   `json:"status"`
	ErrMsg          string `json:"error_msg"`
	CreateTime      string `json:"create_time"`
	PicURL          string `json:"pic_url"`
	VideoURL        string `json:"video_url"`
	VideoFirstFrame string `json:"video_first_frame"`
	Diamond         int64  `json:"diamond"`
	IsDeleted       int8   `json:"is_deleted,omitempty"`
	DeletedAt       string `json:"deleted_at,omitempty"`
	ExpectedTime    string `json:"expected_finish_time"`
	ExpectedTimeStr string `json:"expected_finish_time_str"`

	// 视频分辨率
	VideoHigh  int64 `json:"video_high,omitempty"`
	VideoWidth int64 `json:"video_width,omitempty"`
	// 图片分辨率
	ImageHigh  int64 `json:"image_high,omitempty"`
	ImageWidth int64 `json:"image_width,omitempty"`
}

func (c *UserWorksController) CreateSongWorks(ctx *gin.Context) {
	var req biz.CreateDanceWorksRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("参数错误"), response.WithSLSLog)
		return
	}
	req.RequestHeaderDto = utils.RequestHeader(ctx)
	req.UserID = utils.GetUserId(ctx)
	req.Cover = req.PetPic
	if req.UserID <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("用户未登录"), response.WithSLSLog)
		return
	}
	if len(req.DeviceId) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("DeviceID是必须的"), response.WithSLSLog)
		return
	}

	// 图像检测
	if req.PetPic == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("PersonPic和PetPic是必须的"), response.WithSLSLog)
		return
	}
	result, err := c.aliGreenClient.CheckImage(req.PetPic)
	if err != nil {
		c.log.Error("图像检测失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("图像检测失败："+err.Error()), response.WithSLSLog)
		return
	}
	if result != "" {
		c.log.Error("图像检测不通过: %s", result)
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(result), response.WithSLSLog)
		return
	}

	if err := c.userWorkBiz.CreateSongWorks(ctx, &req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("创建失败："+err.Error()), response.WithSLSLog)
		return
	}
	response.Response(ctx, nil, nil, nil, response.WithSLSLog)
}

func (c *UserWorksController) CreateDanceWorks(ctx *gin.Context) {
	var req biz.CreateDanceWorksRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("参数错误"), response.WithSLSLog)
		return
	}
	req.RequestHeaderDto = utils.RequestHeader(ctx)
	req.UserID = utils.GetUserId(ctx)
	req.Cover = req.PetPic
	if req.UserID <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("用户未登录"), response.WithSLSLog)
		return
	}
	if len(req.DeviceId) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("DeviceID是必须的"), response.WithSLSLog)
		return
	}

	// 图像检测
	if req.PetPic == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("PersonPic和PetPic是必须的"), response.WithSLSLog)
		return
	}
	result, err := c.aliGreenClient.CheckImage(req.PetPic)
	if err != nil {
		c.log.Error("图像检测失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("图像检测失败："+err.Error()), response.WithSLSLog)
		return
	}
	if result != "" {
		c.log.Error("图像检测不通过: %s", result)
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(result), response.WithSLSLog)
		return
	}

	if err := c.userWorkBiz.CreateDanceWorks(ctx, &req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("创建失败："+err.Error()), response.WithSLSLog)
		return
	}
	response.Response(ctx, nil, nil, nil, response.WithSLSLog)
}

// CreateSingleImageWorks 创建单图写真作品
func (c *UserWorksController) CreateSingleImageWorks(ctx *gin.Context) {
	var req biz.CreateSingleImageWorksRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("参数错误"), response.WithSLSLog)
		return
	}

	req.RequestHeaderDto = utils.RequestHeader(ctx)
	req.UserID = utils.GetUserId(ctx)
	req.Cover = req.Image

	if req.UserID <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("用户未登录"), response.WithSLSLog)
		return
	}
	if len(req.DeviceId) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("DeviceID是必须的"), response.WithSLSLog)
		return
	}

	// 图像检测
	if req.Image == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("未上传图像"), response.WithSLSLog)
		return
	}
	result, err := c.aliGreenClient.CheckImage(req.Image)
	if err != nil {
		c.log.Error("图像检测失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("图像检测失败："+err.Error()), response.WithSLSLog)
		return
	}
	if result != "" {
		c.log.Error("图像检测不通过: %s", result)
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(result), response.WithSLSLog)
		return
	}

	//if err := c.userWorkBiz.CreatePicWorks(ctx, &req); err != nil {
	//	response.Response(ctx, nil, nil, errpkg.NewMiddleError("创建失败："+err.Error()), response.WithSLSLog)
	//	return
	//}

	response.Response(ctx, nil, nil, nil, response.WithSLSLog)
}

// CreatePicWorks 创建图片作品
func (c *UserWorksController) CreatePicWorks(ctx *gin.Context) {
	var req biz.CreatePicWorksRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("参数错误"), response.WithSLSLog)
		return
	}
	req.RequestHeaderDto = utils.RequestHeader(ctx)
	req.UserID = utils.GetUserId(ctx)
	req.Cover = req.PersonPic
	if req.UserID <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("用户未登录"), response.WithSLSLog)
		return
	}
	if len(req.DeviceId) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("DeviceID是必须的"), response.WithSLSLog)
		return
	}

	// 图像检测
	if req.PersonPic == "" || req.PetPic == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("PersonPic和PetPic是必须的"), response.WithSLSLog)
		return
	}
	result, err := c.aliGreenClient.CheckImage(req.PersonPic)
	if err != nil {
		c.log.Error("图像检测失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("图像检测失败："+err.Error()), response.WithSLSLog)
		return
	}
	if result != "" {
		c.log.Error("图像检测不通过: %s", result)
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(result), response.WithSLSLog)
		return
	}
	result, err = c.aliGreenClient.CheckImage(req.PetPic)
	if err != nil {
		c.log.Error("图像检测失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("图像检测失败："+err.Error()), response.WithSLSLog)
		return
	}
	if result != "" {
		c.log.Error("图像检测不通过: %s", result)
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(result), response.WithSLSLog)
		return
	}

	if err := c.userWorkBiz.CreatePicWorks(ctx, &req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("创建失败："+err.Error()), response.WithSLSLog)
		return
	}
	response.Response(ctx, nil, nil, nil, response.WithSLSLog)
}

// GetUserWorksList 获取用户作品列表
func (c *UserWorksController) GetUserWorksList(ctx *gin.Context) {
	var req UserWorksListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("参数错误: "+err.Error()), response.WithSLSLog)
		return
	}

	// 获取用户ID
	req.UserID = utils.GetUserId(ctx)
	if req.UserID <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("用户未登录"), response.WithSLSLog)
		return
	}
	qury := dto.UserWorks{
		UserID: uint64(req.UserID),
		// Status:   model.StatusEnabled, // 只查询成功的作品
		Page:      req.Page,
		PageSize:  20,
		IsDeleted: model.StatusDisabled, // 只查询未删除的作品
	}
	// 调用业务逻辑
	result, err := c.userWorkBiz.GetUserWorksList(ctx, &qury)
	if err != nil {
		c.log.Error("获取用户作品列表失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("获取用户作品列表失败"), response.WithSLSLog)
		return
	}
	// list 转换为 UserWorksListResponse
	var output []*UserWorksListResponse
	for _, item := range result {
		var expectedTime, expectedTimeStr string
		if item.ExpectedFinishTime != nil {
			expectedTime = item.ExpectedFinishTime.Format("2006-01-02 15:04:05")
			expectedTimeStr = formatExpectedTime(*item.ExpectedFinishTime)
		}
		item := &UserWorksListResponse{
			ID:              item.ID,
			UserID:          item.UserID,
			WorkType:        item.WorkType,
			Cover:           utils.EnsureHttpsPrefix(item.Cover),
			TemplateID:      item.TemplateID,
			TemplateName:    item.Template.Name,
			Status:          int8(item.Status),
			ErrMsg:          item.ErrMsg,
			CreateTime:      item.UpdatedAt.Format("2006-01-02 15:04:05"),
			PicURL:          utils.EnsureHttpsPrefix(item.PicURL),
			VideoURL:        utils.EnsureHttpsPrefix(item.VideoURL),
			VideoFirstFrame: utils.EnsureHttpsPrefix(item.VideoFirstFrame),
			Diamond:         item.Diamond,
			ExpectedTime:    expectedTime,
			ExpectedTimeStr: expectedTimeStr,
			VideoHigh:       item.VideoHigh,
			VideoWidth:      item.VideoWidth,
			ImageHigh:       item.ImageHigh,
			ImageWidth:      item.ImageWidth,
		}
		// if item.Status == int8(model.StatusDisabled) {
		// 	item.ErrMsg = "生成失败"
		// }
		output = append(output, item)
	}

	response.Response(ctx, nil, output, nil, response.WithSLSLog)
}

// GetUserWorkDetail 获取用户作品详情
func (c *UserWorksController) GetUserWorkDetail(ctx *gin.Context) {
	var req UserWorkDetailRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("参数错误: "+err.Error()), response.WithSLSLog)
		return
	}

	// 获取用户ID
	req.UserID = utils.GetUserId(ctx)
	if req.UserID <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("用户未登录"), response.WithSLSLog)
		return
	}

	// 查询作品详情
	qury := &dto.UserWorks{
		ID:        req.WorkID,
		UserID:    uint64(req.UserID),
		IsDeleted: model.StatusDisabled, // 只查询未删除的作品
	}

	result, err := c.userWorkBiz.GetUserWorksList(ctx, qury)
	if err != nil {
		c.log.Error("获取用户作品详情失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("获取作品详情失败"), response.WithSLSLog)
		return
	}

	// 检查作品是否存在
	if len(result) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("作品不存在或无权限访问"), response.WithSLSLog)
		return
	}

	item := result[0]

	// 转换为响应格式
	var expectedTime, expectedTimeStr string
	if item.ExpectedFinishTime != nil {
		expectedTime = item.ExpectedFinishTime.Format("2006-01-02 15:04:05")
		expectedTimeStr = formatExpectedTime(*item.ExpectedFinishTime)
	}

	output := &UserWorksListResponse{
		ID:              item.ID,
		UserID:          item.UserID,
		WorkType:        item.WorkType,
		Cover:           utils.EnsureHttpsPrefix(item.Cover),
		TemplateID:      item.TemplateID,
		TemplateName:    item.Template.Name,
		Status:          int8(item.Status),
		ErrMsg:          "",
		CreateTime:      item.CreateAt.Format("2006-01-02 15:04:05"),
		PicURL:          utils.EnsureHttpsPrefix(item.PicURL),
		VideoURL:        utils.EnsureHttpsPrefix(item.VideoURL),
		VideoFirstFrame: utils.EnsureHttpsPrefix(item.VideoFirstFrame),
		Diamond:         item.Diamond,
		ExpectedTime:    expectedTime,
		ExpectedTimeStr: expectedTimeStr,
		VideoHigh:       item.VideoHigh,
		VideoWidth:      item.VideoWidth,
		ImageHigh:       item.ImageHigh,
		ImageWidth:      item.ImageWidth,
	}

	// 根据状态设置错误信息
	if item.Status == model.StatusDisabled {
		output.ErrMsg = "生成失败"
	}

	response.Response(ctx, nil, output, nil, response.WithSLSLog)
}

// UserWorkDetailRequest 用户作品详情请求
type UserWorkDetailRequest struct {
	WorkID uint64 `form:"work_id" binding:"required"`
	UserID int64  `json:"user_id"` // 由中间件注入
}

// DeleteUserWorkRequest 删除用户作品请求
type DeleteUserWorkRequest struct {
	WorkID uint64 `form:"work_id" binding:"required"`
	UserID int64  `json:"user_id"` // 由中间件注入
}

// DeleteUserWork 删除用户作品
func (c *UserWorksController) DeleteUserWork(ctx *gin.Context) {
	var req DeleteUserWorkRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		c.log.Error("删除用户作品失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("参数错误"), response.WithSLSLog)
		return
	}

	// 获取用户ID
	req.UserID = utils.GetUserId(ctx)
	if req.UserID <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("用户未登录"), response.WithSLSLog)
		return
	}

	// 调用业务逻辑删除作品
	if err := c.userWorkBiz.DeleteUserWork(ctx, req.UserID, req.WorkID); err != nil {
		c.log.Error("删除用户作品失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("删除失败: "+err.Error()), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, "删除成功", nil, response.WithSLSLog)
}
