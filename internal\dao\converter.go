package dao

import (
	"chongli/internal/model"
	"chongli/internal/service/dto"
)

// ModelTaskToDTO 将 model.Task 转换为 dto.TaskDTO
// func ModelTaskToDTO(task *model.Task) *dto.TaskDTO {
// 	if task == nil {
// 		return nil
// 	}
// 	return &dto.TaskDTO{
// 		ID:          task.ID,
// 		TaskType:    task.TaskType,
// 		Status:      task.Status,
// 		CurrentStep: task.CurrentStep,
// 		RetryCount:  task.RetryCount,
// 		ErrorMsg:    task.ErrorMsg,
// 		CreatedAt:   task.CreatedAt,
// 		UpdatedAt:   task.UpdatedAt,
// 		UserID:      task.UserID,
// 		DeviceID:    task.DeviceID,
// 		WorkID:      task.WorkID,
// 	}
// }

// ModelUserWorksToDTO 将 model.UserWorks 转换为 dto.UserWorks
func ModelUserWorksToDTO(work *model.UserWorks) *dto.UserWorks {
	if work == nil {
		return nil
	}
	userWork := &dto.UserWorks{
		ID:                 work.ID,
		UserID:             work.UserID,
		WorkType:           work.WorkType,
		Cover:              work.Cover,
		TemplateID:         work.TemplateID,
		Status:             model.StatusFlag(work.Status),
		ErrMsg:             work.ErrMsg,
		CreateAt:           work.CreateAt,
		UpdatedAt:          work.UpdatedAt,
		PicURL:             work.PicURL,
		VideoURL:           work.VideoURL,
		Diamond:            work.Diamond,
		IsDeleted:          model.StatusFlag(work.IsDeleted),
		DeletedAt:          work.DeletedAt,
		ExpectedFinishTime: work.ExpectedFinishTime,
		VideoFirstFrame:    work.VideoFirstFrame,
		VideoHigh:          work.VideoHigh,
		VideoWidth:         work.VideoWidth,
		ImageHigh:          work.ImageHigh,
		ImageWidth:         work.ImageWidth,
	}

	// 如果预加载了User，转换用户信息
	if work.User.ID != 0 {
		userWork.User = &dto.UserInfoDto{
			ID:              work.User.ID,
			DeviceId:        work.User.DeviceId,
			Avatar:          work.User.Avatar,
			Username:        work.User.Username,
			Phone:           work.User.Phone,
			Diamond:         work.User.Diamond,
			IsVip:           work.User.IsVip,
			VipType:         work.User.VipType,
			RegisterType:    work.User.RegisterType,
			RegisterVersion: work.User.RegisterVersion,
			Channel:         work.User.Channel,
			Ip:              work.User.IP,
			IPLocation:      work.User.IPLocation,
			CreateAt:        work.User.CreateAt,
			UpdateAt:        work.User.UpdateAt,
			IsDelete:        work.User.IsDelete,
		}
	}

	// 如果预加载了Template，转换模板信息
	if work.Template.ID != 0 {
		userWork.Template = &dto.TemplateDto{
			ID:            work.Template.ID,
			Name:          work.Template.Name,
			CoverURL:      work.Template.CoverURL,
			VideoCoverURL: work.Template.VideoCoverURL,
			Status:        work.Template.Status,
			SortOrder:     work.Template.SortOrder,
			MaxVersionInt: work.Template.MaxVersionInt,
			CategoryID:    work.Template.CategoryID,
			MainClass:     work.Template.MainClass,
			CreateAt:      work.Template.CreateAt,
			UpdatedAt:     work.Template.UpdatedAt,
			DiamondCost:   work.Template.DiamondCost,
			MaxVersion:    work.Template.MaxVersion,
			MakeType:      work.Template.MakeType,
			VideoHigh:     work.Template.VideoHigh,
			VideoWidth:    work.Template.VideoWidth,
			ImageHigh:     work.Template.ImageHigh,
			ImageWidth:    work.Template.ImageWidth,
		}
	}

	return userWork
}
