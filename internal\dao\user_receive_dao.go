package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"errors"
	"gorm.io/gorm"
	"time"
)

type userReceiveRepo struct {
	log *logger.Logger
	db  *gorm.DB
}

func NewUserReceiveRepo(bootStrap *component.BootStrap) repo.UserReceiveRepo {
	return &userReceiveRepo{
		log: bootStrap.Log,
		db:  bootStrap.Driver.GetMysqlDb(),
	}
}

func (d *userReceiveRepo) getDb(tx []*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return d.db
}

// convertModel2Dto 转换模型为DTO
func (d *userReceiveRepo) convertModel2Dto(userReceive *model.UserReceive) *dto.UserReceiveDto {
	return &dto.UserReceiveDto{
		ID:         userReceive.ID,
		UserID:     userReceive.UserID,
		DeviceID:   userReceive.DeviceID,
		ReceiveNum: userReceive.ReceiveNum,
		ReceiveAt:  userReceive.ReceiveAt,
		Version:    userReceive.Version,
		Channel:    userReceive.Channel,
		CreateAt:   userReceive.CreateAt,
	}
}

// CreateUserReceive 创建用户领取记录
func (d *userReceiveRepo) CreateUserReceive(req *dto.CreateUserReceiveRequest, tx ...*gorm.DB) (*dto.UserReceiveDto, error) {
	now := time.Now()

	userReceive := &model.UserReceive{
		UserID:     req.UserID,
		DeviceID:   req.DeviceID,
		ReceiveNum: req.ReceiveNum,
		ReceiveAt:  now,
		Version:    req.Version,
		Channel:    req.Channel,
		CreateAt:   now,
	}

	if err := d.getDb(tx).Model(&model.UserReceive{}).Create(userReceive).Error; err != nil {
		d.log.Error("创建用户领取记录错误: %v", err.Error())
		return nil, err
	}

	return d.convertModel2Dto(userReceive), nil
}

// GetUserReceiveById 根据ID查询用户领取记录
func (d *userReceiveRepo) GetUserReceiveById(id int64, tx ...*gorm.DB) (*dto.UserReceiveDto, error) {
	var userReceive model.UserReceive
	if err := d.getDb(tx).Model(&model.UserReceive{}).Where("id = ?", id).First(&userReceive).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("查询用户领取记录错误: %v", err.Error())
		return nil, err
	}
	return d.convertModel2Dto(&userReceive), nil
}

// UpdateUserReceive 更新用户领取记录
func (d *userReceiveRepo) UpdateUserReceive(req *dto.UpdateUserReceiveRequest, tx ...*gorm.DB) error {
	if req.ID == 0 {
		return errors.New("用户领取记录ID不能为空")
	}

	var updates = make(map[string]any)

	if req.UserID != 0 {
		updates["user_id"] = req.UserID
	}

	if req.DeviceID != "" {
		updates["device_id"] = req.DeviceID
	}

	if req.ReceiveNum != 0 {
		updates["receive_num"] = req.ReceiveNum
	}

	if req.Version != "" {
		updates["version"] = req.Version
	}

	if req.Channel != "" {
		updates["channel"] = req.Channel
	}

	if len(updates) == 0 {
		return nil
	}

	if err := d.getDb(tx).Model(&model.UserReceive{}).Where("id = ?", req.ID).Updates(updates).Error; err != nil {
		d.log.Error("更新用户领取记录错误: %v", err.Error())
		return err
	}
	return nil
}

// DeleteUserReceive 删除用户领取记录（物理删除）
func (d *userReceiveRepo) DeleteUserReceive(id int64, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.UserReceive{}).Where("id = ?", id).Delete(&model.UserReceive{}).Error; err != nil {
		d.log.Error("删除用户领取记录错误: %v", err.Error())
		return err
	}
	return nil
}

// GetUserReceiveByUserId 根据用户ID查询用户领取记录
func (d *userReceiveRepo) GetUserReceiveByUserId(userId int64, tx ...*gorm.DB) ([]*dto.UserReceiveDto, error) {
	var userReceives []*model.UserReceive
	if err := d.getDb(tx).Model(&model.UserReceive{}).
		Where("user_id = ?", userId).
		Order("create_at DESC").
		Find(&userReceives).Error; err != nil {
		d.log.Error("根据用户ID查询用户领取记录错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var dtoList []*dto.UserReceiveDto
	for _, userReceive := range userReceives {
		dtoList = append(dtoList, d.convertModel2Dto(userReceive))
	}

	return dtoList, nil
}

// GetUserReceiveByDeviceId 根据设备ID查询用户领取记录
func (d *userReceiveRepo) GetUserReceiveByDeviceId(deviceId string, tx ...*gorm.DB) ([]*dto.UserReceiveDto, error) {
	var userReceives []*model.UserReceive
	if err := d.getDb(tx).Model(&model.UserReceive{}).Where("device_id = ?", deviceId).Order("create_at DESC").Find(&userReceives).Error; err != nil {
		d.log.Error("根据设备ID查询用户领取记录错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var dtoList []*dto.UserReceiveDto
	for _, userReceive := range userReceives {
		dtoList = append(dtoList, d.convertModel2Dto(userReceive))
	}

	return dtoList, nil
}

// PageUserReceive 分页查询用户领取记录
func (d *userReceiveRepo) PageUserReceive(req *dto.UserReceivePageRequest) (*dto.UserReceivePageResponse, error) {
	var userReceives []*model.UserReceive
	var total int64

	query := d.db.Model(&model.UserReceive{})

	// 添加查询条件
	if req.UserID != 0 {
		query = query.Where("user_id = ?", req.UserID)
	}

	if req.DeviceID != "" {
		query = query.Where("device_id LIKE ?", "%"+req.DeviceID+"%")
	}

	if req.Version != "" {
		query = query.Where("version = ?", req.Version)
	}

	if req.Channel != "" {
		query = query.Where("channel = ?", req.Channel)
	}

	if !req.BeginAt.IsZero() {
		query = query.Where("receive_at >= ?", req.BeginAt)
	}

	if !req.EndAt.IsZero() {
		query = query.Where("receive_at <= ?", req.EndAt)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		d.log.Error("查询用户领取记录总数失败: %v", err)
		return nil, err
	}

	// 获取列表
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("create_at DESC").Find(&userReceives).Error; err != nil {
		d.log.Error("查询用户领取记录列表失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var dtoList []*dto.UserReceiveDto
	for _, userReceive := range userReceives {
		dtoList = append(dtoList, d.convertModel2Dto(userReceive))
	}

	return &dto.UserReceivePageResponse{
		Total: total,
		List:  dtoList,
	}, nil
}

// ListUserReceive 列表查询用户领取记录
func (d *userReceiveRepo) ListUserReceive(userId int64, deviceId string, tx ...*gorm.DB) ([]*dto.UserReceiveDto, error) {
	var userReceives []*model.UserReceive
	query := d.getDb(tx).Model(&model.UserReceive{})

	if userId != 0 {
		query = query.Where("user_id = ?", userId)
	}

	if deviceId != "" {
		query = query.Where("device_id = ?", deviceId)
	}

	if err := query.Order("create_at DESC").Find(&userReceives).Error; err != nil {
		d.log.Error("列表查询用户领取记录失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var dtoList []*dto.UserReceiveDto
	for _, userReceive := range userReceives {
		dtoList = append(dtoList, d.convertModel2Dto(userReceive))
	}

	return dtoList, nil
}

// BatchDeleteUserReceive 批量删除用户领取记录
func (d *userReceiveRepo) BatchDeleteUserReceive(ids []int64, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.UserReceive{}).Where("id IN (?)", ids).Delete(&model.UserReceive{}).Error; err != nil {
		d.log.Error("批量删除用户领取记录错误: %v", err.Error())
		return err
	}
	return nil
}

// GetUserReceiveCount 获取用户领取记录总数
func (d *userReceiveRepo) GetUserReceiveCount(userId int64, deviceId string, tx ...*gorm.DB) (int64, error) {
	var count int64
	query := d.getDb(tx).Model(&model.UserReceive{})

	if userId != 0 {
		query = query.Where("user_id = ?", userId)
	}

	if deviceId != "" {
		query = query.Where("device_id = ?", deviceId)
	}

	if err := query.Count(&count).Error; err != nil {
		d.log.Error("获取用户领取记录总数失败: %v", err)
		return 0, err
	}

	return count, nil
}
