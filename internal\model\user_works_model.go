package model

import "time"

// UserWorks 用户作品
type UserWorks struct {
	ID                 uint64     `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
	UserID             uint64     `gorm:"column:user_id;NOT NULL" json:"user_id"`
	WorkType           string     `gorm:"column:work_type;NOT NULL" json:"work_type"`
	Cover              string     `gorm:"column:cover;NOT NULL" json:"cover"`
	TemplateID         uint64     `gorm:"column:template_id;NOT NULL" json:"template_id"`
	Status             int8       `gorm:"column:status;default:0;NOT NULL" json:"status"`
	ErrMsg             string     `gorm:"column:error_msg;type:varchar(1024);default:null" json:"error_msg"`
	CreateAt           time.Time  `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_at"`
	UpdatedAt          time.Time  `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;NOT NULL;autoUpdateTime" json:"update_at"`
	DeletedAt          *time.Time `gorm:"column:deleted_at;default:null" json:"deleted_at"`
	IsDeleted          int8       `gorm:"column:is_deleted;default:-1;NOT NULL" json:"is_deleted"`
	ExpectedFinishTime *time.Time `gorm:"column:expected_finish_time;default:null" json:"expected_finish_time"`
	PicURL             string     `gorm:"column:pic_url;NOT NULL" json:"pic_url"`
	VideoURL           string     `gorm:"column:video_url;NOT NULL" json:"video_url"`
	VideoFirstFrame    string     `gorm:"column:video_first_frame;default:'';NOT NULL" json:"video_first_frame"`
	Diamond            int64      `gorm:"column:diamond;NOT NULL" json:"diamond"`
	VideoHigh          int64      `gorm:"column:video_high;default:0;NOT NULL" json:"video_high"`   // 视频分辨率高
	VideoWidth         int64      `gorm:"column:video_width;default:0;NOT NULL" json:"video_width"` // 视频分辨率宽
	ImageHigh          int64      `gorm:"column:image_high;default:0;NOT NULL" json:"image_high"`   // 图片分辨率高
	ImageWidth         int64      `gorm:"column:image_width;default:0;NOT NULL" json:"image_width"` // 图片分辨率宽

	Template AITemplate `gorm:"foreignKey:TemplateID;references:ID"`
	User     User       `gorm:"foreignKey:UserID;references:ID"`
}

// TableName -
func (u *UserWorks) TableName() string {
	return "user_works"
}
