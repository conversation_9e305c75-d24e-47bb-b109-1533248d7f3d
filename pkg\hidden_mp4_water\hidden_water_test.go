package hiddenwater

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"testing"
	"time"
)

const (
	inputPath  = "/mnt/c/Users/<USER>/Downloads/ok.mp4"
	outputPath = "/mnt/c/Users/<USER>/Downloads/111.mp4"
)

var testWatermark = []byte("chongli")

// 仅负责写入水印并生成输出文件
func TestAddWatermark(t *testing.T) {
	t.Logf("run at: %v", time.Now())

	if _, err := os.Stat(inputPath); err != nil {
		t.Ski<PERSON>("input mp4 not found  at %s: %v (skipping)", inputPath, err)
	}

	if err := AddWatermark(inputPath, outputPath, testWatermark); err != nil {
		t.Fatalf("AddWatermark failed: %v", err)
	}

	if st, err := os.Stat(outputPath); err != nil || st.<PERSON><PERSON>() == 0 {
		if err != nil {
			t.Fatalf("output file not found: %v", err)
		}
		t.Fatalf("output file size is 0")
	}
	t.Logf("output file size is ok")
}

// 仅负责验证水印是否存在且匹配
func TestExtractWatermark(t *testing.T) {
	url := "https://chongli-cdn.51wnl-cq.com/chongli-dance-videos/2025/08/19/23de71b2e78c16c9028a4452862ab8af.mp4"
	t.Logf("run at: %v", time.Now())

	// 先从 URL 下载到 outputPath（覆盖写入）
	resp, err := http.Get(url)
	if err != nil {
		t.Fatalf("download failed: %v", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		t.Fatalf("download bad status: %s", resp.Status)
	}
	f, err := os.Create(outputPath)
	if err != nil {
		t.Fatalf("create output failed: %v", err)
	}
	if _, err := io.Copy(f, resp.Body); err != nil {
		f.Close()
		t.Fatalf("write output failed: %v", err)
	}
	if err := f.Close(); err != nil {
		t.Fatalf("close output failed: %v", err)
	}

	got, err := ExtractWatermark(outputPath)
	if err != nil {
		t.Fatalf("ExtractWatermark failed: %v", err)
	}
	if string(got) != string(testWatermark) {
		t.Fatalf("watermark mismatch: got %q want %q", string(got), string(testWatermark))
	}

	// 美化的成功输出
	fmt.Printf("\n================ Watermark Verification ================\n")
	fmt.Printf(" Success\n")
	fmt.Printf("- Source URL : %s\n", url)
	fmt.Printf("- Output Path: %s\n", outputPath)
	fmt.Printf("- Watermark  : %q\n", string(got))
	fmt.Printf("=======================================================\n\n")
}
