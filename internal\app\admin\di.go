package admin

import (
	"chongli/internal/app/admin/controller"
	"chongli/internal/app/admin/router"

	"github.com/google/wire"
)

// RouterSet 管理后台路由依赖注入集合
var RouterSet = wire.NewSet(
	router.NewAdmin<PERSON><PERSON><PERSON>,
	router.NewUserManageRouter,
	router.NewBusinessRouter,
	router.NewTemplateManageRouter,
	router.NewUserWorkRouter,
)

// ControllerSet 管理后台控制器依赖注入集合
var ControllerSet = wire.NewSet(
	controller.NewAuthController,
	controller.NewAccountController,
	controller.NewChannelController,
	controller.NewConfigController,
	controller.NewPopupController,
	controller.NewCommonController,
	controller.NewUserManageController,
	controller.NewChannelPopupController,
	controller.NewConfigChannelRelationController,
	controller.NewVersionController,
	controller.NewGoodsBaseController,
	controller.NewGoodsController,
	controller.NewTemplateManageController,
	controller.NewPayOrder<PERSON>anage<PERSON><PERSON><PERSON><PERSON>,
	controller.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	controller.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	controller.NewBannerController,
	controller.NewUserWorkController,
	controller.NewUserBindGetuiController,
)