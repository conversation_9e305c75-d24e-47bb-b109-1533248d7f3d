package dto

import (
	"chongli/internal/model"
	"time"
)

// UserBindGetuiDto 用户个推绑定DTO
type UserBindGetuiDto struct {
	ID        int       `json:"id" form:"id"`
	UserID    int       `json:"user_id" form:"user_id" binding:"required"`
	ClientID  string    `json:"client_id" form:"client_id" binding:"required,max=64"`
	DeviceID  string    `json:"device_id" form:"device_id" binding:"required,max=64"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	IsDelete  model.StatusFlag `json:"is_delete" form:"is_delete"`

	// For pagination and sorting
	Page     int `json:"page" form:"page" binding:"min=1"`
	PageSize int `json:"page_size" form:"page_size" binding:"min=1,max=100"`
	OrderBy  string `json:"order_by" form:"order_by"`     // Field to sort by
	Sort     string `json:"sort" form:"sort"`             // asc or desc
}
