package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"context"
	"errors"
	"gorm.io/gorm"
	"time"
)

// templateViewMakeLogRepoImpl 实现 TemplateViewMakeLogRepo
// create_at 使用 Unix 秒时间戳，落库/查询时按 int64 处理

type templateViewMakeLogRepoImpl struct {
	db *gorm.DB
}

func NewTemplateViewMakeLogRepo(bootStrap *component.BootStrap) repo.TemplateViewMakeLogRepo {
	return &templateViewMakeLogRepoImpl{db: bootStrap.Driver.GetMysqlDb()}
}

func (r *templateViewMakeLogRepoImpl) getDB(tx ...*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return r.db
}

func (r *templateViewMakeLogRepoImpl) Create(_ context.Context, log *dto.TemplateViewMakeLogDTO, tx ...*gorm.DB) error {
	if log == nil {
		return errors.New("log is nil")
	}
	m := &model.TemplateViewMakeLog{
		ID:         log.ID,
		TemplateID: log.TemplateID,
		Type:       log.Type,
		CreateAt:   log.CreateAt,
	}
	return r.getDB(tx...).Create(m).Error
}

func (r *templateViewMakeLogRepoImpl) Update(_ context.Context, id int64, updates map[string]any, tx ...*gorm.DB) error {
	return r.getDB(tx...).Model(&model.TemplateViewMakeLog{}).Where("id = ?", id).Updates(updates).Error
}

func (r *templateViewMakeLogRepoImpl) Delete(_ context.Context, id int64, tx ...*gorm.DB) error {
	return r.getDB(tx...).Where("id = ?", id).Delete(&model.TemplateViewMakeLog{}).Error
}

func (r *templateViewMakeLogRepoImpl) buildQuery(db *gorm.DB, q *dto.TemplateViewMakeLogQueryDTO) *gorm.DB {
	if q == nil {
		return db
	}
	if q.ID > 0 {
		db = db.Where("id = ?", q.ID)
	}
	if q.TemplateID > 0 {
		db = db.Where("template_id = ?", q.TemplateID)
	}
	if q.Type > 0 {
		db = db.Where("type = ?", q.Type)
	}
	if q.BeginAt > 0 {
		db = db.Where("create_at >= ?", q.BeginAt)
	}
	if q.EndAt > 0 {
		db = db.Where("create_at <= ?", q.EndAt)
	}
	return db
}

func (r *templateViewMakeLogRepoImpl) GetOne(_ context.Context, q *dto.TemplateViewMakeLogQueryDTO, tx ...*gorm.DB) (*dto.TemplateViewMakeLogDTO, error) {
	db := r.getDB(tx...)
	db = r.buildQuery(db, q)
	var m model.TemplateViewMakeLog
	if err := db.First(&m).Error; err != nil {
		return nil, err
	}
	return &dto.TemplateViewMakeLogDTO{
		ID:         m.ID,
		TemplateID: m.TemplateID,
		Type:       m.Type,
		CreateAt:   m.CreateAt,
	}, nil
}

func (r *templateViewMakeLogRepoImpl) List(_ context.Context, q *dto.TemplateViewMakeLogQueryDTO, tx ...*gorm.DB) ([]*dto.TemplateViewMakeLogDTO, error) {
	db := r.getDB(tx...)
	db = r.buildQuery(db, q)
	db = db.Order("id desc")
	var list []model.TemplateViewMakeLog
	if err := db.Find(&list).Error; err != nil {
		return nil, err
	}
	res := make([]*dto.TemplateViewMakeLogDTO, 0, len(list))
	for _, m := range list {
		item := m
		res = append(res, &dto.TemplateViewMakeLogDTO{
			ID:         item.ID,
			TemplateID: item.TemplateID,
			Type:       item.Type,
			CreateAt:   item.CreateAt,
		})
	}
	return res, nil
}

func (r *templateViewMakeLogRepoImpl) Page(_ context.Context, q *dto.TemplateViewMakeLogQueryDTO, tx ...*gorm.DB) ([]*dto.TemplateViewMakeLogDTO, int64, error) {
	db := r.getDB(tx...)
	db = r.buildQuery(db, q)

	var total int64
	if err := db.Model(&model.TemplateViewMakeLog{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if q.PageNum <= 0 {
		q.PageNum = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 20
	}
	if q.PageSize > 1000 {
		q.PageSize = 1000
	}

	offset := (q.PageNum - 1) * q.PageSize
	db = db.Order("id desc").Offset(offset).Limit(q.PageSize)

	var list []model.TemplateViewMakeLog
	if err := db.Find(&list).Error; err != nil {
		return nil, 0, err
	}

	res := make([]*dto.TemplateViewMakeLogDTO, 0, len(list))
	for _, m := range list {
		item := m
		res = append(res, &dto.TemplateViewMakeLogDTO{
			ID:         item.ID,
			TemplateID: item.TemplateID,
			Type:       item.Type,
			CreateAt:   item.CreateAt,
		})
	}
	return res, total, nil
}

func (r *templateViewMakeLogRepoImpl) Count(_ context.Context, q *dto.TemplateViewMakeLogQueryDTO, tx ...*gorm.DB) (int64, error) {
	db := r.getDB(tx...)
	db = r.buildQuery(db, q)
	var cnt int64
	if err := db.Model(&model.TemplateViewMakeLog{}).Count(&cnt).Error; err != nil {
		return 0, err
	}
	return cnt, nil
}

// BatchStats 批量统计多个模板在给定时间范围内的 浏览/制作 数量
func (r *templateViewMakeLogRepoImpl) BatchStats(_ context.Context, templateIDs []int64, beginAt, endAt int64, tx ...*gorm.DB) ([]*dto.TemplateStatsDTO, error) {
	if len(templateIDs) == 0 {
		return []*dto.TemplateStatsDTO{}, nil
	}

	db := r.getDB(tx...)

	// 使用一个查询获取所有模板的浏览和制作统计
	type StatsResult struct {
		TemplateID int64 `gorm:"column:template_id"`
		Type       int   `gorm:"column:type"`
		Count      int64 `gorm:"column:count"`
	}

	var results []StatsResult
	query := `
		SELECT
			template_id,
			type,
			COUNT(*) as count
		FROM template_view_make_log
		WHERE template_id IN (?)
			AND type IN (1, 2)
			AND create_at >= ?
			AND create_at <= ?
		GROUP BY template_id, type
	`

	if err := db.Raw(query, templateIDs, beginAt, endAt).Scan(&results).Error; err != nil {
		return nil, err
	}

	// 构建结果映射
	statsMap := make(map[int64]*dto.TemplateStatsDTO)

	// 初始化所有模板的统计数据
	for _, templateID := range templateIDs {
		statsMap[templateID] = &dto.TemplateStatsDTO{
			TemplateID: templateID,
			ViewCount:  0,
			MakeCount:  0,
		}
	}

	// 填充统计数据
	for _, result := range results {
		if stats, exists := statsMap[result.TemplateID]; exists {
			if result.Type == 1 { // 浏览
				stats.ViewCount = result.Count
			} else if result.Type == 2 { // 制作
				stats.MakeCount = result.Count
			}
		}
	}

	// 转换为切片
	statsList := make([]*dto.TemplateStatsDTO, 0, len(templateIDs))
	for _, templateID := range templateIDs {
		statsList = append(statsList, statsMap[templateID])
	}

	return statsList, nil
}

// DailyStatsByTemplateInRange 获取单个模板在时间范围内按天统计的 浏览/制作 数量（补齐缺失天数）
func (r *templateViewMakeLogRepoImpl) DailyStatsByTemplateInRange(_ context.Context, templateID int64, beginAt, endAt int64, tx ...*gorm.DB) ([]dto.TemplateDailyStatsDTO, error) {
	if templateID == 0 {
		return []dto.TemplateDailyStatsDTO{}, nil
	}

	db := r.getDB(tx...)

	// 原始查询：按天聚合浏览/制作数量
	type DayAgg struct {
		Day   string `gorm:"column:day"`
		Type  int    `gorm:"column:type"`
		Count int64  `gorm:"column:count"`
	}

	var rows []DayAgg
	// 以应用本地时区对齐天粒度，避免MySQL时区差异导致的日期错位
	// 将 create_at 加上本地时区偏移后再取 DATE
	_, offsetSec := time.Now().In(time.Local).Zone()
	query := `
		SELECT DATE(FROM_UNIXTIME(create_at + ?)) AS day, type, COUNT(*) AS count
		FROM template_view_make_log
		WHERE template_id = ? AND type IN (1,2) AND create_at >= ? AND create_at <= ?
		GROUP BY day, type
	`

	if err := db.Raw(query, offsetSec, templateID, beginAt, endAt).Scan(&rows).Error; err != nil {
		return nil, err
	}

	// 将结果映射到 map[day]view/make
	statsByDay := map[string]struct{ view, make int64 }{}
	for _, rrow := range rows {
		// 确保日期格式为 YYYY-MM-DD，处理可能的时区格式
		dayStr := rrow.Day
		if len(dayStr) > 10 {
			// 如果包含时间部分，只取日期部分
			if t, err := time.Parse(time.RFC3339, dayStr); err == nil {
				dayStr = t.Format("2006-01-02")
			} else if len(dayStr) >= 10 {
				dayStr = dayStr[:10] // 简单截取前10位
			}
		}

		item := statsByDay[dayStr]
		if rrow.Type == model.ViewTemplateType {
			item.view = rrow.Count
		} else if rrow.Type == model.MakeTemplateType {
			item.make = rrow.Count
		}
		statsByDay[dayStr] = item
	}

	// 补齐从 beginAt 到 endAt 的每天
	start := time.Unix(beginAt, 0).In(time.Local)
	end := time.Unix(endAt, 0).In(time.Local)
	// 规范到零点
	start = time.Date(start.Year(), start.Month(), start.Day(), 0, 0, 0, 0, start.Location())
	end = time.Date(end.Year(), end.Month(), end.Day(), 0, 0, 0, 0, end.Location())

	var list []dto.TemplateDailyStatsDTO
	for d := start; !d.After(end); d = d.AddDate(0, 0, 1) {
		dayStr := d.Format("2006-01-02")
		item := statsByDay[dayStr]
		list = append(list, dto.TemplateDailyStatsDTO{
			Date:      dayStr,
			ViewCount: item.view,
			MakeCount: item.make,
		})
	}

	return list, nil
}
