package controller

import (
	"chongli/component"
	"chongli/component/apollo"
	"chongli/internal/app/api/biz"
	"chongli/internal/model"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	errpkg "chongli/pkg/error"
	"chongli/pkg/jwt"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"chongli/pkg/utils"
	"fmt"
	"image"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"mime/multipart"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type CommonController struct {
	log              *logger.Logger
	config           *apollo.Config
	configBiz        *biz.ConfigBiz
	guiyinApiService *biz.GuiyinApiService
	popupBiz         *biz.PopupBiz
	getuiBiz         *biz.GeTuiBiz
	bannerBiz        *biz.BannerBiz
	commonService    *service.CommonService
	userService      *service.UserService
}

func NewCommonController(
	bootStrap *component.BootStrap,
	configBiz *biz.ConfigBiz,
	guiyinApiService *biz.GuiyinApiService,
	popupBiz *biz.PopupBiz,
	getuiBiz *biz.GeTuiBiz,
	bannerBiz *biz.BannerBiz,
	commonService *service.CommonService,
	userService *service.UserService,
) *CommonController {
	return &CommonController{
		log:              bootStrap.Log,
		config:           bootStrap.Config,
		configBiz:        configBiz,
		guiyinApiService: guiyinApiService,
		popupBiz:         popupBiz,
		getuiBiz:         getuiBiz,
		bannerBiz:        bannerBiz,
		commonService:    commonService,
		userService:      userService,
	}
}

// ConfigList 获取配置列表
func (c *CommonController) ConfigList(ctx *gin.Context) {
	var req biz.ConfigListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("请求参数错误: "+err.Error()), response.WithSLSLog)
		return
	}
	header := utils.RequestHeader(ctx)
	req.RequestHeaderDto = header
	if len(req.DeviceId) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("DeviceID是必须的"), response.WithSLSLog)
		return
	}
	// 调用业务逻辑层
	responseData, bizErr := c.configBiz.ConfigList(&req)
	if bizErr != nil {
		c.log.Error("获取配置列表失败: %v", bizErr.Error())
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("获取列表失败"), response.WithSLSLog)
		return
	}

	response.Response(ctx, req, responseData, nil, response.WithSLSLog)

}

// GetVersion 版本检查更新
func (c *CommonController) GetVersion(ctx *gin.Context) {
	header := utils.RequestHeader(ctx)
	channel, err := utils.ClearPlatform(header.Channel)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("请求参数错误: "+err.Error()), response.WithSLSLog)
		return
	}

	data, err := c.configBiz.GetVersion(channel)
	if err != nil {
		c.log.Error("获取版本失败: %v", err.Error())
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("获取版本失败"), response.WithSLSLog)
		return
	}
	response.Response(ctx, nil, data, nil, response.WithSLSLog)
}

// BindAttributeData 绑定归因数据
func (c *CommonController) BindAttributeData(ctx *gin.Context) {

	rawData, err := ctx.GetRawData()
	if err != nil {
		ctx.String(http.StatusBadRequest, "读取失败: %v", err)
		return
	}

	if len(ctx.Request.Header.Get("deviceId")) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("DeviceID是必须的"), response.WithSLSLog)
		return
	}
	bindDto := biz.DeviceBindInfo{
		DeviceID: ctx.Request.Header.Get("deviceId"),
		AllData:  string(rawData),
		Channel:  ctx.Request.Header.Get("channel"),
	}
	if bindDto.DeviceID == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("DeviceID不能为空"), response.WithSLSLog)
		return
	}
	if bindDto.Channel == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("Channel不能为空"), response.WithSLSLog)
		return
	}
	data, errFromService := c.guiyinApiService.BindAttributeData(ctx, &bindDto)
	if errFromService != nil {
		c.log.Error("BindAttributeData error: %v", errFromService)
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("绑定归因数据失败"), response.WithSLSLog)
		return
	}
	response.Response(ctx, nil, map[string]any{"channel_id": data}, nil, response.WithSLSLog)
}

func (c *CommonController) getUserIdFromJwtToken(ctx *gin.Context) (int64, error) {
	token := ctx.Request.Header.Get("X-Token")
	if token == "" {
		return 0, fmt.Errorf("token is empty")
	}

	jwtSecret := c.config.JwtSecret
	j := jwt.NewJWT(jwtSecret)
	claims, errParse := j.ParserToken(token)
	if errParse != nil {
		// token过期
		if errParse.Error() == jwt.ValidationErrorExpired {
			return 0, fmt.Errorf("token expired")
		}
		return 0, fmt.Errorf("token invalid")
	}

	// 用户注销校验
	userId := claims.MetaData["user_id"].(float64)
	userInfo, err := c.userService.GetUserInfoByUid(int64(userId))
	if err != nil {
		return 0, fmt.Errorf("get user info failed")
	}
	if userInfo.IsDelete == int8(model.StatusEnabled) {
		return 0, fmt.Errorf("user is cancelled")
	}

	return int64(userId), nil
}

func (c *CommonController) Popup(ctx *gin.Context) {
	header := utils.RequestHeader(ctx)
	userId, _ := c.getUserIdFromJwtToken(ctx)

	if len(header.DeviceId) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("DeviceID是必须的"), response.WithSLSLog)
		return
	}
	if len(header.Channel) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("Channel是必须的"), response.WithSLSLog)
		return
	}

	req := biz.PopupListRequest{
		RequestHeaderDto: *header,
	}

	list, err := c.popupBiz.GetPopupList(ctx, &req)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("获取弹窗列表失败"), response.WithSLSLog)
		return
	}

	// 对已登录用户做数据处理
	if userId > 0 {
		list = c.handlePopupListForLoginUser(list, userId)
	}

	response.Response(ctx, nil, list, nil, response.WithSLSLog)
}

func (c *CommonController) handlePopupListForLoginUser(popupList []*biz.PopupListResponse, userId int64) []*biz.PopupListResponse {
	// 获取用户的领取记录
	userReceiveList, err := c.userService.GetUserReceiveRecord(userId)
	if err != nil {
		return popupList
	}

	if len(userReceiveList) > 0 {
		// 获取最新的记录
		userReceiveRecord := userReceiveList[0]

		// 判断最新记录的领取时间是否是今天内的
		if userReceiveRecord.ReceiveAt.Format("2006-01-02") == time.Now().Format("2006-01-02") {
			var newPopupList []*biz.PopupListResponse

			// 将弹窗中位置为 “give_daily” 的过滤掉
			for _, popup := range popupList {
				if popup.Location != "give_daily" {
					newPopupList = append(newPopupList, popup)
				}
			}
			return newPopupList
		}
	}

	return popupList
}

func (c *CommonController) BindGeTui(ctx *gin.Context) {
	var req dto.BindGeTuiCidRequest
	if errBind := ctx.ShouldBind(&req); errBind != nil {
		response.Response(ctx, req, nil, errpkg.NewMiddleError(response.BadRequest), response.WithSLSLog)
		return
	}
	userID := utils.GetUserId(ctx)
	if userID <= 0 {
		logger.Log().Error("获取用户id失败")
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(response.UserIdError), response.WithSLSLog)
		return
	}
	req.UserId = userID
	data, err := c.getuiBiz.BindGeTui(&req)
	response.Response(ctx, req, data, err, response.WithSLSLog)
}

func (c *CommonController) GetBannerByLocation(ctx *gin.Context) {
	location := ctx.Param("location")
	if location == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("位置不能为空"), response.WithSLSLog)
		return
	}

	// 从请求头中获取版本号
	version := ctx.GetHeader("version")
	if version == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("版本号不能为空"), response.WithSLSLog)
		return
	}

	// 调用业务逻辑层
	banners, bizErr := c.bannerBiz.GetBannerByLocation(version, location)
	if bizErr != nil {
		c.log.Error("获取banner失败: %v", bizErr.Error())
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("获取banner失败"), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, banners, nil, response.WithSLSLog)
}

// GetIndexBanner 获取首页banner
func (c *CommonController) GetIndexBanner(ctx *gin.Context) {
	// 从请求头中获取版本号
	version := ctx.GetHeader("version")
	if version == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("版本号不能为空"), response.WithSLSLog)
		return
	}

	// 调用业务逻辑层
	banners, bizErr := c.bannerBiz.GetIndexBannerList(version)
	if bizErr != nil {
		c.log.Error("获取首页banner失败: %v", bizErr.Error())
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("获取首页banner列表失败"), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, banners, nil, response.WithSLSLog)
}

// GetPayBanner 获取支付页面banner
func (c *CommonController) GetPayBanner(ctx *gin.Context) {
	// 从请求头中获取版本号
	version := ctx.GetHeader("version")
	if version == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("版本号不能为空"), response.WithSLSLog)
		return
	}

	// 调用业务逻辑层
	banners, bizErr := c.bannerBiz.GetPayBannerList(version)
	if bizErr != nil {
		c.log.Error("获取支付页面banner失败: %v", bizErr.Error())
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("获取支付页面banner失败"), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, banners, nil, response.WithSLSLog)
}

// Upload 文件上传
func (c *CommonController) Upload(ctx *gin.Context) {
	var request dto.UploadMultiRequestDto
	if errBind := ctx.ShouldBind(&request); errBind != nil {
		c.log.Error("绑定参数失败: %v", errBind.Error())
		response.Response(ctx, request, nil, errpkg.NewMiddleError("绑定参数失败"), response.WithSLSLog)
		return
	}
	// 如果长传的文件大小大于10M则返回错误
	for _, file := range request.Files {
		if file.Size >= 15*1024*1024 {
			response.Response(ctx, request, nil, errpkg.NewLowError("上传文件大小大于15MB"), response.WithSLSLog)
			return
		}

		// 检查图片文件的宽高比
		if err := c.checkImageAspectRatio(file); err != nil {
			response.Response(ctx, request, nil, err, response.WithSLSLog)
			return
		}
	}
	urls, err := c.commonService.PicUpload(ctx, &request)

	// 为 url 添加 https://
	for i, url := range urls {
		urls[i] = "https://" + url
	}

	response.Response(ctx, request, urls, err, response.WithSLSLog)
}

// checkImageAspectRatio 检查图片宽高比是否在0.5~2.0范围内
func (c *CommonController) checkImageAspectRatio(file *multipart.FileHeader) errpkg.IError {
	filename := strings.ToLower(file.Filename)

	// 只检查图片文件
	if !strings.HasSuffix(filename, ".jpg") && !strings.HasSuffix(filename, ".jpeg") &&
		!strings.HasSuffix(filename, ".png") && !strings.HasSuffix(filename, ".gif") {
		return nil // 非图片文件，跳过检查
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		c.log.Error("打开文件失败: %v", err.Error())
		return errpkg.NewLowError("文件读取失败")
	}
	defer src.Close()

	// 解码图片获取尺寸
	img, _, err := image.DecodeConfig(src)
	if err != nil {
		c.log.Error("解码图片失败: %v", err.Error())
		return errpkg.NewLowError("图片格式不正确")
	}

	// 计算宽高比
	aspectRatio := float64(img.Width) / float64(img.Height)

	// 检查宽高比是否在0.5~2.0范围内
	if aspectRatio < 0.5 || aspectRatio > 2.0 {
		return errpkg.NewLowError("图片宽高比需要为 0.5 ~ 2.0")
	}

	return nil
}
