package hiddenwater

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"net/url"
	"os"
	"path"
	"path/filepath"

	"github.com/Eyevinn/mp4ff/mp4"
)

// 添加水印到MP4的udta box
func AddWatermark(input, output string, watermark []byte) (string, error) {
	if len(input) == 0 {
		return "", errors.New("添加水印失败，输入文件为空")
	}
	data, err := os.ReadFile(input)
	if err != nil {
		return "", err
	}
	if output == "" {
		u, err := url.Parse(input)
		if err != nil {
			return "", err
		}
		ext := path.Ext(u.Path)
		if len(ext) > 10 || ext == "" {
			ext = ".tmp"
		}
		hash := md5.Sum([]byte(input))
		fileName := hex.EncodeToString(hash[:]) + ext
		output = filepath.Join(os.TempDir(), fileName)
	}

	// 解析MP4
	file, err := mp4.DecodeFile(bytes.NewReader(data))
	if err != nil {
		return "", err
	}

	// 创建udta box，如果已经有可以替换
	udta := &mp4.UdtaBox{}
	// UnknownBox 大小需要包含 box header(8字节) + 负载
	unk := mp4.CreateUnknownBox("wmrk", uint64(8+len(watermark)), watermark)
	udta.AddChild(unk) // 用自定义类型 'wmrk' 保存水印

	// 将udta box加到moov box下
	moov := file.Moov
	if moov == nil {
		return "", fmt.Errorf("找不到 moov box")
	}
	moov.AddChild(udta)

	// 写入文件
	// 设置编码模式为写回完整的 box 树，避免丢失 mdat/编解码配置导致文件无法播放
	file.FragEncMode = mp4.EncModeBoxTree
	var buf bytes.Buffer
	if err := file.Encode(&buf); err != nil {
		return "", err
	}
	return output, os.WriteFile(output, buf.Bytes(), 0644)
}

// 从MP4提取水印
func ExtractWatermark(input string) ([]byte, error) {
	data, err := os.ReadFile(input)
	if err != nil {
		return nil, err
	}

	file, err := mp4.DecodeFile(bytes.NewReader(data))
	if err != nil {
		return nil, err
	}

	moov := file.Moov
	if moov == nil {
		return nil, fmt.Errorf("找不到 moov box")
	}

	for _, child := range moov.Children {
		if child.Type() == "udta" {
			if udta, ok := child.(*mp4.UdtaBox); ok {
				for _, box := range udta.Children {
					if box.Type() == "wmrk" {
						if unk, ok := box.(*mp4.UnknownBox); ok {
							return unk.Payload(), nil
						}
					}
				}
			}
		}
	}
	return nil, fmt.Errorf("未找到水印")
}
