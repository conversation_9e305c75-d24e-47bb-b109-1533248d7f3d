package service

import (
	"chongli/internal/service/dto"
	"chongli/pkg/aliyun"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/qiniu"
	"chongli/pkg/response"
	"chongli/pkg/stringchar"
	"chongli/pkg/utils"
	"fmt"
	"image"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type CommonService struct {
	aliGreenClient *aliyun.AliGreenClient
}

func NewCommonService(
	aliGreenClient *aliyun.AliGreenClient,
) *CommonService {
	return &CommonService{
		aliGreenClient: aliGreenClient,
	}
}

// PicUpload 文件上传
func (cs *CommonService) PicUpload(ctx *gin.Context, req *dto.UploadMultiRequestDto) (resp []string, err errpkg.IError) {
	var _err error
	_, exists := ctx.Get("user_id")
	if !exists {
		return []string{}, errpkg.NewLowError(response.PermissionDeniedError)
	}
	for i := range req.Files {
		fileName := stringchar.Md5Hex([]byte(req.Files[i].Filename), 16)
		filePath := fmt.Sprintf("%s%s%s", time.Now().Format("2006/01/02/"), fileName, path.Ext(req.Files[i].Filename))
		// ImgName, _err := qiniu.UploadFile(0, req.File, filePath)
		url, err := qiniu.UploadFile(0, req.Files[i], filePath)
		if err != nil {
			logger.Log().Error("上传文件失败: %v", err)
			return []string{}, errpkg.NewLowError(response.UploadFileError)
		}
		// 通过文件后缀判断是否为图片类型
		ext := strings.ToLower(path.Ext(req.Files[i].Filename))
		// 常见图片格式后缀
		imageExts := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"}

		isImage := slices.Contains(imageExts, ext)

		if isImage {
			errMsg := ""
			// 检查图片是否违规
			if errMsg, _err = cs.aliGreenClient.CheckImage(url); _err != nil {
				logger.Log().Error("CheckImage error: %v", _err)
				return []string{}, errpkg.NewLowError(response.UploadFileError)
			}
			if errMsg != "" {
				return []string{}, errpkg.NewLowError(errMsg)
			}
		} else {
			logger.Log().Error("上传文件不是图片,%v", req.Files[i].Filename)
		}
		resp = append(resp, url)
	}

	return resp, nil
}

// GetVideoOrImageWidthHigh 获取视频或图片文件的分辨率宽高
func (cs *CommonService) GetVideoOrImageWidthHigh(fileURL string) (width, height int, err errpkg.IError) {
	// 检查URL是否为空
	if fileURL == "" {
		return 0, 0, errpkg.NewLowError("文件URL不能为空")
	}

	// 确保URL是HTTPS格式
	fileURL = utils.EnsureHttpsPrefix(fileURL)

	// 下载文件到临时目录
	tempFilePath, downloadErr := cs.downloadFileToTemp(fileURL)
	if downloadErr != nil {
		logger.Log().Error("下载文件失败: %v", downloadErr)
		return 0, 0, errpkg.NewLowError("下载文件失败")
	}
	defer os.Remove(tempFilePath) // 确保临时文件被删除

	// 根据文件扩展名判断文件类型
	ext := strings.ToLower(path.Ext(tempFilePath))

	// 图片格式
	imageExts := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"}
	// 视频格式
	videoExts := []string{".mp4", ".avi", ".mov", ".mkv", ".flv", ".wmv", ".webm"}

	if slices.Contains(imageExts, ext) {
		// 处理图片文件
		return cs.getImageDimensions(tempFilePath)
	} else if slices.Contains(videoExts, ext) {
		// 处理视频文件
		return cs.getVideoDimensions(tempFilePath)
	} else {
		// 尝试通过文件内容判断类型
		if width, height, imgErr := cs.getImageDimensions(tempFilePath); imgErr == nil {
			return width, height, nil
		}
		if width, height, vidErr := cs.getVideoDimensions(tempFilePath); vidErr == nil {
			return width, height, nil
		}
		return 0, 0, errpkg.NewLowError("不支持的文件格式")
	}
}

// downloadFileToTemp 下载文件到临时目录
func (cs *CommonService) downloadFileToTemp(fileURL string) (string, error) {
	// 发起HTTP请求
	resp, err := http.Get(fileURL)
	if err != nil {
		return "", fmt.Errorf("请求文件失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("下载文件失败，状态码: %d", resp.StatusCode)
	}

	// 从URL中提取文件扩展名
	ext := path.Ext(fileURL)
	if ext == "" {
		// 如果URL中没有扩展名，尝试从Content-Type推断
		contentType := resp.Header.Get("Content-Type")
		switch {
		case strings.Contains(contentType, "image/jpeg"):
			ext = ".jpg"
		case strings.Contains(contentType, "image/png"):
			ext = ".png"
		case strings.Contains(contentType, "image/gif"):
			ext = ".gif"
		case strings.Contains(contentType, "video/mp4"):
			ext = ".mp4"
		case strings.Contains(contentType, "video/"):
			ext = ".mp4" // 默认视频扩展名
		default:
			ext = ".tmp"
		}
	}

	// 创建临时文件
	tempFile, err := os.CreateTemp("", "media_*"+ext)
	if err != nil {
		return "", fmt.Errorf("创建临时文件失败: %w", err)
	}
	defer tempFile.Close()

	// 将响应体内容写入临时文件
	_, err = io.Copy(tempFile, resp.Body)
	if err != nil {
		os.Remove(tempFile.Name())
		return "", fmt.Errorf("保存文件失败: %w", err)
	}

	return tempFile.Name(), nil
}

// getImageDimensions 获取图片文件的尺寸
func (cs *CommonService) getImageDimensions(filePath string) (width, height int, err errpkg.IError) {
	file, openErr := os.Open(filePath)
	if openErr != nil {
		return 0, 0, errpkg.NewLowError("打开图片文件失败")
	}
	defer file.Close()

	// 使用image.DecodeConfig获取图片配置信息（不解码整个图片，效率更高）
	config, _, decodeErr := image.DecodeConfig(file)
	if decodeErr != nil {
		return 0, 0, errpkg.NewLowError("解码图片失败，可能不是有效的图片文件")
	}

	return config.Width, config.Height, nil
}

// getVideoDimensions 获取视频文件的尺寸
func (cs *CommonService) getVideoDimensions(filePath string) (width, height int, err errpkg.IError) {
	// 检查FFmpeg是否可用
	if !cs.isFFmpegAvailable() {
		return 0, 0, errpkg.NewLowError("FFmpeg未安装或不可用")
	}

	// 使用ffprobe获取视频信息
	cmd := exec.Command("ffprobe",
		"-v", "quiet",
		"-print_format", "csv=p=0",
		"-select_streams", "v:0",
		"-show_entries", "stream=width,height",
		filePath)

	output, cmdErr := cmd.Output()
	if cmdErr != nil {
		logger.Log().Error("执行ffprobe失败: %v", cmdErr)
		return 0, 0, errpkg.NewLowError("获取视频信息失败")
	}

	// 解析输出结果，格式为: width,height
	outputStr := strings.TrimSpace(string(output))
	if outputStr == "" {
		return 0, 0, errpkg.NewLowError("无法获取视频尺寸信息")
	}

	// 使用正则表达式解析宽高
	re := regexp.MustCompile(`(\d+),(\d+)`)
	matches := re.FindStringSubmatch(outputStr)
	if len(matches) != 3 {
		return 0, 0, errpkg.NewLowError("解析视频尺寸信息失败")
	}

	width, widthErr := strconv.Atoi(matches[1])
	if widthErr != nil {
		return 0, 0, errpkg.NewLowError("解析视频宽度失败")
	}

	height, heightErr := strconv.Atoi(matches[2])
	if heightErr != nil {
		return 0, 0, errpkg.NewLowError("解析视频高度失败")
	}

	return width, height, nil
}

// isFFmpegAvailable 检查FFmpeg是否可用
func (cs *CommonService) isFFmpegAvailable() bool {
	_, err := exec.LookPath("ffprobe")
	return err == nil
}
