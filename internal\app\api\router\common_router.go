package router

import (
	"chongli/internal/app/api/controller"
	"chongli/internal/middleware"

	"github.com/gin-gonic/gin"
)

type CommonRouter struct {
	commonCtrl        *controller.CommonController
	JwtAuthMiddleware *middleware.JWTAuthMiddleware
}

func NewCommonRouter(
	engine *gin.Engine,
	commonCtrl *controller.CommonController,
	JwtAuthMiddleware *middleware.JWTAuthMiddleware,
) *CommonRouter {

	router := &CommonRouter{
		commonCtrl:        commonCtrl,
		JwtAuthMiddleware: JwtAuthMiddleware,
	}

	// 绑定归因数据
	engine.POST("api/guiyin/bind", commonCtrl.BindAttributeData)

	// 获取配置
	engine.GET("api/config", commonCtrl.ConfigList)

	// 获取版本
	engine.GET("api/version/", commonCtrl.GetVersion)

	// 获取弹窗
	engine.GET("api/popup/list", commonCtrl.Popup)

	// 获取首页banner
	engine.GET("api/index/banner", commonCtrl.GetIndexBanner)

	// 获取支付页面banner
	engine.GET("api/pay/banner", commonCtrl.GetPayBanner)

	// 根据 location 获取不同位置 banner
	engine.GET("api/banner/:location", commonCtrl.GetBannerByLocation)

	{
		// 上传文件
		upload := engine.Group("api/upload")
		upload.Use(JwtAuthMiddleware.JWTAuth())
		upload.POST("", commonCtrl.Upload)
	}

	{
		// 绑定个推
		getui := engine.Group("api/getui")
		getui.Use(JwtAuthMiddleware.JWTAuth())
		getui.POST("bind", commonCtrl.BindGeTui)
	}

	return router
}
