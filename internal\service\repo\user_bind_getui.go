package repo

import "chongli/internal/service/dto"

type UserBindGetuiRepo interface {
	BindUserIdAndCid(userId int64, cid string) error
	UpdateBind(userId int64, cid string) error
	GetBindInfoByUserId(userId int64) (*dto.UserBindGetuiDto, error)
	GetBindInfoByCid(cid string) (*dto.UserBindGetuiDto, error)
	DeleteBindByUserId(userId int64) error

	// New methods
	GetOne(query *dto.UserBindGetuiDto) (*dto.UserBindGetuiDto, error)
	Select(query *dto.UserBindGetuiDto) ([]*dto.UserBindGetuiDto, error)
	Count(query *dto.UserBindGetuiDto) (int64, error)
	Update(id int, updates map[string]interface{}) (*dto.UserBindGetuiDto, error)
	Add(data *dto.UserBindGetuiDto) (*dto.UserBindGetuiDto, error)
	Delete(id int) error
	Page(req *dto.GetuiListRequest) ([]*dto.UserBindGetuiInfoDto, int64, error)
}
