package controller

import (
	"chongli/component"
	"chongli/internal/app/api/biz"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"chongli/pkg/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type TemplateController struct {
	log         *logger.Logger
	templateBiz *biz.TemplateBiz
}

func NewTemplateController(
	bootStrap *component.BootStrap,
	templateBiz *biz.TemplateBiz,
) *TemplateController {
	return &TemplateController{
		log:         bootStrap.Log,
		templateBiz: templateBiz,
	}
}

func (t *TemplateController) GetMainClassTemplateList(ctx *gin.Context) {
	mainClassIdStr := ctx.Param("id")
	if mainClassIdStr == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("main class id is required"), response.WithSLSLog)
		return
	}

	version := ctx.GetHeader("version")
	if version == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("version is required"), response.WithSLSLog)
		return
	}

	page := ctx.Query("page")
	size := ctx.Query("size")
	if page == "" || size == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("page and size are required"), response.WithSLSLog)
		return
	}
	pageInt, err := strconv.Atoi(page)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("invalid page"), response.WithSLSLog)
		return
	}
	sizeInt, err := strconv.Atoi(size)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("invalid size"), response.WithSLSLog)
		return
	}

	mainClassId, err := strconv.ParseInt(mainClassIdStr, 10, 64)
	if err != nil || mainClassId <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("invalid main class id"), response.WithSLSLog)
		return
	}

	maxVersionInt := utils.VersionToVersionInt(version)
	if maxVersionInt <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("invalid version"), response.WithSLSLog)
		return
	}

	data, err := t.templateBiz.GetMainClassTemplateList(ctx, &dto.TemplateAIQueryDTO{
		Template: dto.TemplateAIDTO{
			MainClass:     int(mainClassId),
			MaxVersionInt: maxVersionInt,
		},
		PageNum:        pageInt,
		PageSize:       sizeInt,
		OrderBy:        "sort_order",
		OrderDirection: "asc",
	})
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(err.Error()), response.WithSLSLog)
		return
	}

	if len(data) == 0 {
		t.log.Warning("No templates found for main class ID: %d", mainClassId)
		data = make([]*dto.TemplateAIDTO, 0)
	}

	var resp []*dto.TemplateDto
	for _, template := range data {
		resp = append(resp, &dto.TemplateDto{
			ID:              template.ID,
			Name:            template.Name,
			CoverURL:        utils.EnsureHttpsPrefix(template.CoverURL),
			VideoCoverURL:   utils.EnsureHttpsPrefix(template.VideoCoverURL),
			VideoFirstFrame: utils.EnsureHttpsPrefix(template.VideoFirstFrame),
			Status:          template.Status,
			SortOrder:       template.SortOrder,
			MaxVersion:      template.MaxVersion,
			CategoryID:      template.CategoryID,
			CreateAt:        template.CreateAt,
			UpdatedAt:       template.UpdatedAt,
			DiamondCost:     template.DiamondCost,
			MainClass:       template.MainClass,
			MakeType:        template.MakeType,
		})
	}

	response.Response(ctx, nil, resp, nil, response.WithSLSLog)
}

func (t *TemplateController) GetCategoryTemplateList(ctx *gin.Context) {
	templateCategoryId := ctx.Param("id")
	if templateCategoryId == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("category id is required"), response.WithSLSLog)
		return
	}

	page := ctx.Query("page")
	size := ctx.Query("size")
	if page == "" || size == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("page and size are required"), response.WithSLSLog)
		return
	}
	pageInt, err := strconv.Atoi(page)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("invalid page"), response.WithSLSLog)
		return
	}
	sizeInt, err := strconv.Atoi(size)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("invalid size"), response.WithSLSLog)
		return
	}

	version := ctx.GetHeader("version")
	if version == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("version is required"), response.WithSLSLog)
		return
	}

	categoryId, err := strconv.ParseInt(templateCategoryId, 10, 64)
	if err != nil || categoryId <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("invalid category id"), response.WithSLSLog)
		return
	}

	maxVersionInt := utils.VersionToVersionInt(version)
	if maxVersionInt <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("invalid version"), response.WithSLSLog)
		return
	}

	data, err := t.templateBiz.GetCategoryTemplateList(ctx, &dto.TemplateAIQueryDTO{
		Template: dto.TemplateAIDTO{
			CategoryID:    uint64(categoryId),
			MaxVersionInt: maxVersionInt,
			Status:        int8(model.StatusEnabled), // 只查询启用的模板
		},
		PageNum:        pageInt,
		PageSize:       sizeInt,
		OrderBy:        "sort_order",
		OrderDirection: "asc",
	})
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(err.Error()), response.WithSLSLog)
		return
	}

	if len(data) == 0 {
		t.log.Warning("No templates found for category ID: %d", categoryId)
		data = make([]*dto.TemplateAIDTO, 0)
	}

	var resp []*dto.TemplateDto
	for _, template := range data {
		resp = append(resp, &dto.TemplateDto{
			ID:              template.ID,
			Name:            template.Name,
			CoverURL:        utils.EnsureHttpsPrefix(template.CoverURL),
			VideoCoverURL:   utils.EnsureHttpsPrefix(template.VideoCoverURL),
			VideoFirstFrame: utils.EnsureHttpsPrefix(template.VideoFirstFrame),
			Status:          template.Status,
			SortOrder:       template.SortOrder,
			MaxVersion:      template.MaxVersion,
			CategoryID:      template.CategoryID,
			CreateAt:        template.CreateAt,
			UpdatedAt:       template.UpdatedAt,
			DiamondCost:     template.DiamondCost,
			MainClass:       template.MainClass,
			MakeType:        template.MakeType,
			VideoHigh:       template.VideoHigh,
			VideoWidth:      template.VideoWidth,
			ImageHigh:       template.ImageHigh,
			ImageWidth:      template.ImageWidth,
		})
	}

	response.Response(ctx, nil, resp, nil, response.WithSLSLog)
}

func (t *TemplateController) GetTemplateDetail(ctx *gin.Context) {
	templateId := ctx.Param("id")
	if templateId == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("template id is required"), response.WithSLSLog)
		return
	}

	id, err := strconv.ParseUint(templateId, 10, 64)
	if err != nil || id <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("invalid template id"), response.WithSLSLog)
		return
	}

	data, err := t.templateBiz.GetTemplateDetail(ctx, id)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(err.Error()), response.WithSLSLog)
		return
	}

	var resp *dto.TemplateDto
	if data != nil {
		resp = &dto.TemplateDto{
			ID:              data.ID,
			Name:            data.Name,
			CoverURL:        data.CoverURL,
			VideoCoverURL:   data.VideoCoverURL,
			VideoFirstFrame: data.VideoFirstFrame,
			Status:          data.Status,
			SortOrder:       data.SortOrder,
			MaxVersion:      data.MaxVersion,
			CategoryID:      data.CategoryID,
			CreateAt:        data.CreateAt,
			UpdatedAt:       data.UpdatedAt,
			DiamondCost:     data.DiamondCost,
			MainClass:       data.MainClass,
			MakeType:        data.MakeType,
			VideoHigh:       data.VideoHigh,
			VideoWidth:      data.VideoWidth,
			ImageHigh:       data.ImageHigh,
			ImageWidth:      data.ImageWidth,
		}
	} else {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("模板内容为空"), response.WithSLSLog)
	}

	response.Response(ctx, nil, resp, nil, response.WithSLSLog)
}

// ViewTemplate 增加模板浏览量
func (t *TemplateController) ViewTemplate(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil || id == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("invalid template id"), response.WithSLSLog)
		return
	}
	if err := t.templateBiz.IncreaseView(ctx, id); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(err.Error()), response.WithSLSLog)
		return
	}
	response.Response(ctx, nil, nil, nil, response.WithSLSLog)
}
