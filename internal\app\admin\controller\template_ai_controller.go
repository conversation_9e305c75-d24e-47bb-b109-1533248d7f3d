package controller

import (
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/response"
	"chongli/pkg/utils"
	"context"
	"encoding/json"
	"strconv"

	"github.com/gin-gonic/gin"
)

type TemplateAIController struct {
	templateAIRepo repo.TemplateAIRepo
	configService  *service.ConfigService
	commonService  *service.CommonService
}

// UpdateScriptDTO 更新AI模板脚本的DTO
type UpdateScriptDTO struct {
	ID            uint64 `json:"id" binding:"required"`             // 模板ID
	VariablesJSON string `json:"variables_json" binding:"required"` // 变量JSON
}

// BatchUpdateTemplateDTO 批量更新AI模板的DTO
type BatchUpdateTemplateDTO struct {
	IDs         []uint64 `json:"ids" binding:"required"` // 模板ID列表
	MaxVersion  string   `json:"max_version"`            // 最大版本
	DiamondCost *int     `json:"diamond_cost"`           // 钻石消耗
	CategoryID  uint64   `json:"category_id"`            // 模板分类
	MainClass   int      `json:"main_class"`             // 所属主分类
	MakeType    string   `json:"make_type"`              // 模板制作类型
	Status      int      `json:"status"`                 // 是否启用
}

func NewTemplateAIController(
	templateAIRepo repo.TemplateAIRepo,
	configService *service.ConfigService,
	commonService *service.CommonService,
) *TemplateAIController {
	return &TemplateAIController{
		templateAIRepo: templateAIRepo,
		configService:  configService,
		commonService:  commonService,
	}
}

// Create 创建AI模板
func (c *TemplateAIController) Create(ctx *gin.Context) {
	var template dto.TemplateAIDTO

	if err := ctx.ShouldBindJSON(&template); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(err.Error()), response.WithNoSLSLog)
		return
	}

	if err := utils.CheckVersion(template.MaxVersion); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, "无效的版本号"), response.WithNoSLSLog)
		return
	}

	template.MaxVersionInt = utils.VersionToVersionInt(template.MaxVersion)

	// 获取分辨率
	if err := c.getResolution(&template); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, "获取分辨率失败"), response.WithNoSLSLog)
		return
	}

	if err := c.templateAIRepo.Create(ctx, &template); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(err.Error()), response.WithNoSLSLog)
		return
	}

	response.Response(ctx, nil, nil, nil, response.WithNoSLSLog)
}

// 获取分辨率
func (c *TemplateAIController) getResolution(template *dto.TemplateAIDTO) error {
	// 获取图片分辨率
	if template.CoverURL != "" {
		width, height, err := c.commonService.GetVideoOrImageWidthHigh(template.CoverURL)
		if err != nil {
			return err
		}
		template.ImageWidth = int64(width)
		template.ImageHigh = int64(height)
	}

	// 获取视频分辨率
	if template.VideoCoverURL != "" {
		width, height, err := c.commonService.GetVideoOrImageWidthHigh(template.VideoCoverURL)
		if err != nil {
			return err
		}
		template.VideoWidth = int64(width)
		template.VideoHigh = int64(height)
	}

	return nil
}

// Update 更新AI模板
func (c *TemplateAIController) Update(ctx *gin.Context) {
	var template dto.TemplateAIDTO
	if err := ctx.ShouldBindJSON(&template); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(err.Error()), response.WithNoSLSLog)
		return
	}

	updates := make(map[string]any)
	if template.Name != "" {
		updates["name"] = template.Name
	}
	if template.CoverURL != "" {
		updates["cover_url"] = template.CoverURL
		newImageWidth, newImageHigh, _ := c.commonService.GetVideoOrImageWidthHigh(template.CoverURL)
		updates["image_width"] = newImageWidth
		updates["image_high"] = newImageHigh
	}
	if template.VideoCoverURL != "" {
		updates["video_cover_url"] = template.VideoCoverURL
		newVideoWidth, newVideoHigh, _ := c.commonService.GetVideoOrImageWidthHigh(template.VideoCoverURL)
		updates["video_width"] = newVideoWidth
		updates["video_high"] = newVideoHigh
	}
	if template.VideoFirstFrame != "" {
		updates["video_first_frame"] = template.VideoFirstFrame
	}
	if template.Status != 0 {
		updates["status"] = template.Status
	}
	if template.SortOrder != 0 {
		updates["sort_order"] = template.SortOrder
	}
	if template.CategoryID != 0 {
		updates["category_id"] = template.CategoryID
	}
	if template.MainClass != 0 {
		updates["main_class"] = template.MainClass
	}
	if template.MakeType != "" {
		updates["make_type"] = template.MakeType
	}

	// 处理VariablesJSON - 将JSONMap转换为json.RawMessage
	// if len(template.VariablesJSON) > 0 {
	// 	// 对于写真类（MainClass == 1），确保包含 workflow_url 字段
	// 	if template.MainClass == 1 {
	// 		if _, ok := template.VariablesJSON["workflow_url"]; !ok {
	// 			template.VariablesJSON["workflow_url"] = "https://chongli-cdn.51wnl-cq.com/workflow/pet2.json"
	// 		}
	// 	}

	// 	variablesBytes, err := json.Marshal(template.VariablesJSON)
	// 	if err != nil {
	// 		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, "JSON序列化失败"), response.WithNoSLSLog)
	// 		return
	// 	}
	// 	variablesJSON := json.RawMessage(variablesBytes)
	// 	updates["variables_json"] = &variablesJSON
	// }

	if template.DiamondCost != 0 {
		updates["diamond_cost"] = template.DiamondCost
	}

	if template.MaxVersion != "" {
		if err := utils.CheckVersion(template.MaxVersion); err != nil {
			response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, "无效的版本号"), response.WithNoSLSLog)
			return
		}
		updates["max_version"] = template.MaxVersion
		updates["max_version_int"] = utils.VersionToVersionInt(template.MaxVersion)
	}

	if err := c.templateAIRepo.Update(ctx, template.ID, updates); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}
	response.Response(ctx, nil, nil, nil, response.WithNoSLSLog)
}

// Delete 删除AI模板
func (c *TemplateAIController) Delete(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, "无效的ID"), response.WithNoSLSLog)
		return
	}
	if err := c.templateAIRepo.Delete(ctx, id); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}
	response.Response(ctx, nil, nil, nil, response.WithNoSLSLog)
}

// GetByID 根据ID获取AI模板
func (c *TemplateAIController) GetByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, "无效的ID"), response.WithNoSLSLog)
		return
	}
	template, err := c.templateAIRepo.GetByID(ctx, id)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}
	response.Response(ctx, nil, template, nil, response.WithNoSLSLog)
}

func (c *TemplateAIController) GetAllTemplates(ctx *gin.Context) {
	templates, err := c.templateAIRepo.ListAll(ctx)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}

	var resp []*dto.TemplateAIDTO
	for _, template := range templates {
		resp = append(resp, &dto.TemplateAIDTO{
			ID:              template.ID,
			Name:            template.Name,
			CategoryID:      template.CategoryID,
			CreateAt:        template.CreateAt,
			UpdatedAt:       template.UpdatedAt,
			DiamondCost:     template.DiamondCost,
			VideoCoverURL:   utils.EnsureHttpsPrefix(template.VideoCoverURL),
			CoverURL:        utils.EnsureHttpsPrefix(template.CoverURL),
			Status:          template.Status,
			SortOrder:       template.SortOrder,
			MaxVersionInt:   template.MaxVersionInt,
			Category:        template.Category,
			MaxVersion:      template.MaxVersion,
			VariablesJSON:   template.VariablesJSON,
			MainClass:       template.MainClass,
			MakeType:        template.MakeType,
			VideoFirstFrame: utils.EnsureHttpsPrefix(template.VideoFirstFrame),
		})
	}

	response.Response(ctx, nil, resp, nil, response.WithNoSLSLog)
}

// ListByQuery 根据条件查询AI模板列表和总数
func (c *TemplateAIController) ListByQuery(ctx *gin.Context) {
	var query dto.TemplateAIQueryDTO
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.BadRequest), response.WithNoSLSLog)
		return
	}

	// 获取列表数据
	templates, err := c.templateAIRepo.ListByQuery(ctx, &query)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}

	// 获取总数
	count, err := c.templateAIRepo.Count(ctx, &query)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}

	var resp []*dto.TemplateAIDTO
	for _, template := range templates {
		resp = append(resp, &dto.TemplateAIDTO{
			ID:              template.ID,
			Name:            template.Name,
			CategoryID:      template.CategoryID,
			MainClass:       template.MainClass,
			CreateAt:        template.CreateAt,
			UpdatedAt:       template.UpdatedAt,
			DiamondCost:     template.DiamondCost,
			VideoCoverURL:   utils.EnsureHttpsPrefix(template.VideoCoverURL),
			CoverURL:        utils.EnsureHttpsPrefix(template.CoverURL),
			Status:          template.Status,
			SortOrder:       template.SortOrder,
			MaxVersionInt:   template.MaxVersionInt,
			Category:        template.Category,
			MaxVersion:      template.MaxVersion,
			VariablesJSON:   template.VariablesJSON,
			MakeType:        template.MakeType,
			VideoFirstFrame: utils.EnsureHttpsPrefix(template.VideoFirstFrame),
		})
	}

	// 构造返回数据
	result := map[string]interface{}{
		"list":  resp,
		"total": count,
	}

	response.Response(ctx, nil, result, nil, response.WithNoSLSLog)
}

// ListAll 查询所有AI模板
func (c *TemplateAIController) ListAll(ctx *gin.Context) {
	templates, err := c.templateAIRepo.ListAll(ctx)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}
	response.Response(ctx, nil, templates, nil, response.WithNoSLSLog)
}

// UpdateScript 更新AI模板脚本（只修改variables_json字段）
func (c *TemplateAIController) UpdateScript(ctx *gin.Context) {
	var updateScript UpdateScriptDTO
	if err := ctx.ShouldBindJSON(&updateScript); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.BadRequest), response.WithNoSLSLog)
		return
	}

	// 只更新variables_json字段
	updates := map[string]interface{}{
		"variables_json": updateScript.VariablesJSON,
	}

	if err := c.templateAIRepo.Update(ctx, updateScript.ID, updates); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}

	response.Response(ctx, nil, nil, nil, response.WithNoSLSLog)
}

// BatchUpdate 批量更新AI模板
func (c *TemplateAIController) BatchUpdate(ctx *gin.Context) {
	var batchUpdate BatchUpdateTemplateDTO
	if err := ctx.ShouldBindJSON(&batchUpdate); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.BadRequest), response.WithNoSLSLog)
		return
	}

	// 验证模板ID列表不能为空
	if len(batchUpdate.IDs) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("模板ID列表不能为空"), response.WithNoSLSLog)
		return
	}

	// 构建更新字段
	updates := make(map[string]any)

	if batchUpdate.MaxVersion != "" {
		updates["max_version"] = batchUpdate.MaxVersion
		updates["max_version_int"] = utils.VersionToVersionInt(batchUpdate.MaxVersion)
	}
	if batchUpdate.DiamondCost != nil {
		updates["diamond_cost"] = *batchUpdate.DiamondCost
	}
	if batchUpdate.CategoryID > 0 {
		updates["category_id"] = batchUpdate.CategoryID
	}
	if batchUpdate.MainClass > 0 {
		updates["main_class"] = batchUpdate.MainClass
	}
	if batchUpdate.MakeType != "" {
		updates["make_type"] = batchUpdate.MakeType
	}
	if batchUpdate.Status != 0 {
		updates["status"] = batchUpdate.Status
	}

	// 执行批量更新
	if err := c.templateAIRepo.BatchUpdate(ctx, batchUpdate.IDs, updates); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}

	response.Response(ctx, nil, nil, nil, response.WithNoSLSLog)
}

func (c *TemplateAIController) GetAllMakeType(ctx *gin.Context) {
	makeTypes, err := c.configService.GetConfigByKeyFromCache(context.Background(), "template_make_type")
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.ConfigNotExit), response.WithNoSLSLog)
		return
	}

	var makeTypeList []*dto.TemplateMakeType
	if err := json.Unmarshal([]byte(makeTypes), &makeTypeList); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}

	response.Response(ctx, nil, makeTypeList, nil, response.WithNoSLSLog)
}
