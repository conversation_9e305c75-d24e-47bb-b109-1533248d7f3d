package model

import (
	"time"
)

// AITemplate AI 内容生成模板
type AITemplate struct {
	ID               uint64            `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
	Name             string            `gorm:"column:name;NOT NULL" json:"name"`
	CoverURL         string            `gorm:"column:cover_url;type:varchar(255)" json:"cover_url"`
	VideoCoverURL    string            `gorm:"column:video_cover_url;type:varchar(255)" json:"video_cover_url"`
	VideoFirstFrame  string            `gorm:"column:video_first_frame;type:varchar(255)" json:"video_first_frame"` // 视频首帧封面
	Status           int8              `gorm:"column:status;default:1;NOT NULL" json:"status"`
	MaxVersion       string            `gorm:"column:max_version;NOT NULL" json:"max_version"`         // 最大适用版本
	MaxVersionInt    int64             `gorm:"column:max_version_int;NOT NULL" json:"max_version_int"` // 最大适用版本int值
	SortOrder        int               `gorm:"column:sort_order;default:0;NOT NULL" json:"sort_order"`
	CategoryID       uint64            `gorm:"column:category_id;type:varchar(50)" json:"category_id"`
	VariablesJSON    string            `gorm:"column:variables_json;type:json" json:"variables_json"`
	CreateAt         time.Time         `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_at"`
	UpdatedAt        time.Time         `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;NOT NULL;autoUpdateTime" json:"update_at"`
	TemplateCategory *TemplateCategory `gorm:"foreignKey:CategoryID;references:ID" json:"template_category"`
	DiamondCost      int               `gorm:"column:diamond_cost;default:0;NOT NULL" json:"diamond_cost"` // 钻石花费
	MainClass        int               `gorm:"column:main_class;default:0;NOT NULL" json:"main_class"`     // 主分类 1-写真 2-唱歌 3-跳舞
	MakeType         string            `gorm:"column:make_type;default:'';NOT NULL" json:"make_type"`      // 模板制作类型，单图/多图等，值为字符串枚举，比如 “single_image”
	VideoHigh        int64             `gorm:"column:video_high;default:0;NOT NULL" json:"video_high"`     // 视频分辨率高
	VideoWidth       int64             `gorm:"column:video_width;default:0;NOT NULL" json:"video_width"`   // 视频分辨率宽
	ImageHigh        int64             `gorm:"column:image_high;default:0;NOT NULL" json:"image_high"`     // 图片分辨率高
	ImageWidth       int64             `gorm:"column:image_width;default:0;NOT NULL" json:"image_width"`   // 图片分辨率宽
}

// TableName 表名称
func (ai *AITemplate) TableName() string {
	return "template_ai"
}
