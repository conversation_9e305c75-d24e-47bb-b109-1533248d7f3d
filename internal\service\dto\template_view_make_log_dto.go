package dto

// TemplateViewMakeLogDTO 模板浏览/制作日志 DTO
// Type: 1-浏览 2-制作
// CreateAt: Unix 时间戳（秒）
type TemplateViewMakeLogDTO struct {
	ID         int64 `json:"id"`
	TemplateID int64 `json:"template_id"`
	Type       int   `json:"type"`
	CreateAt   int64 `json:"create_at"`
}

// TemplateViewMakeLogQueryDTO 查询/分页 DTO
// 如果 PageNum/PageSize 均为0，则不分页，返回所有符合条件的数据
// BeginAt/EndAt 为 Unix 时间戳（秒），用于时间范围查询（闭区间）
type TemplateViewMakeLogQueryDTO struct {
	ID         int64 `json:"id" form:"id"`
	TemplateID int64 `json:"template_id" form:"template_id"`
	Type       int   `json:"type" form:"type"`

	BeginAt int64 `json:"begin_at" form:"begin_at"`
	EndAt   int64 `json:"end_at" form:"end_at"`

	// 分页
	PageNum  int `json:"page_num" form:"page_num"`
	PageSize int `json:"page_size" form:"page_size"`
}

// TemplateStatsDTO 模板统计数据 DTO
type TemplateStatsDTO struct {
	TemplateID int64 `json:"template_id"` // 模板ID
	ViewCount  int64 `json:"view_count"`  // 浏览量
	MakeCount  int64 `json:"make_count"`  // 制作量
}

// TemplateDailyStatsDTO 模板按天统计数据 DTO（用于本月每日统计）
type TemplateDailyStatsDTO struct {
	Date      string `json:"date"`       // 日期 YYYY-MM-DD
	ViewCount int64  `json:"view_count"` // 浏览量
	MakeCount int64  `json:"make_count"` // 制作量
}

// TemplateViewMakeStatsRequest 获取多个模板统计请求
type TemplateViewMakeStatsRequest struct {
	TemplateIDs []int64 `json:"template_ids" form:"template_ids" binding:"required"`
}

// TemplateViewMakeStatsItem 单个模板的统计结果
type TemplateViewMakeStatsItem struct {
	TemplateID     int64                   `json:"template_id"`      // 模板ID
	ThisMonthDaily []TemplateDailyStatsDTO `json:"this_month_daily"` // 本月开始到现在的每日浏览/制作量
	ThisWeek       *TemplateStatsDTO       `json:"this_week"`        // 本周（周一至周日）总浏览/制作量
	Total          *TemplateStatsDTO       `json:"total"`            // 历史总浏览/制作量
}

// TemplateViewMakeStatsResponse 获取多个模板统计响应
type TemplateViewMakeStatsResponse struct {
	Items []TemplateViewMakeStatsItem `json:"items"`
}
