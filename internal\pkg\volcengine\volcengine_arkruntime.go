package volcengine

import (
	"context"
	"errors"
	"fmt"
	"os"

	"github.com/volcengine/volcengine-go-sdk/service/arkruntime"
	"github.com/volcengine/volcengine-go-sdk/service/arkruntime/model"
	"github.com/volcengine/volcengine-go-sdk/volcengine"
)

// 任务状态常量枚举
const (
	TaskStatusQueued    = "queued"    // 排队中
	TaskStatusRunning   = "running"   // 任务运行中
	TaskStatusCancelled = "cancelled" // 取消任务，取消状态24h自动删除（只支持排队中状态的任务被取消）
	TaskStatusSucceeded = "succeeded" // 任务成功
	TaskStatusFailed    = "failed"    // 任务失败
)

// ContentGenerationTaskManager 内容生成任务管理器
type ContentGenerationTaskManager struct {
	client *arkruntime.Client
}

// NewContentGenerationTaskManager 创建新的内容生成任务管理器
func NewContentGenerationTaskManager(apiKey ...string) *ContentGenerationTaskManager {
	var key string
	if len(apiKey) > 0 && apiKey[0] != "" {
		key = apiKey[0]
	} else {
		key = os.Getenv("ARK_API_KEY")
	}

	client := arkruntime.NewClientWithApiKey(key)
	return &ContentGenerationTaskManager{
		client: client,
	}
}

// CreateTaskRequest 创建任务请求结构
type CreateTaskRequest struct {
	Model       string         `json:"model"`                  // 模型名称
	TextPrompt  string         `json:"text_prompt"`            // 文本提示
	ImageURL    string         `json:"image_url,omitempty"`    // 图片URL（可选）
	Ratio       string         `json:"ratio,omitempty"`        // 画面比例
	Duration    int            `json:"duration,omitempty"`     // 时长
	Resolution  string         `json:"resolution,omitempty"`   // 分辨率
	ExtraParams map[string]any `json:"extra_params,omitempty"` // 额外参数
}

// CreateTaskResponse 创建任务响应结构
type CreateTaskResponse struct {
	ID    string `json:"id"`    // 任务ID
	Error string `json:"error"` // 错误信息
}

// QueryTaskResponse 查询任务响应结构
type QueryTaskResponse struct {
	Status   string `json:"status"`    // 任务状态：queued(排队中)、running(运行中)、cancelled(已取消)、succeeded(成功)、failed(失败)
	VideoURL string `json:"video_url"` // 生成的视频URL
	Error    string `json:"error"`     // 错误信息
}

// CreateTask 创建内容生成任务
func (m *ContentGenerationTaskManager) CreateTask(ctx context.Context, request *CreateTaskRequest) (*CreateTaskResponse, error) {
	if request == nil {
		return &CreateTaskResponse{Error: "请求参数不能为空"}, errors.New("请求参数不能为空")
	}

	if request.Model == "" {
		return &CreateTaskResponse{Error: "模型名称不能为空"}, errors.New("模型名称不能为空")
	}

	if request.TextPrompt == "" {
		return &CreateTaskResponse{Error: "文本提示不能为空"}, errors.New("文本提示不能为空")
	}
	if request.Duration != 0 {
		request.TextPrompt = fmt.Sprintf("%s --dur %d", request.TextPrompt, request.Duration)
	}
	// 构建请求内容
	content := []*model.CreateContentGenerationContentItem{
		{
			Type: "text",
			Text: volcengine.String(request.TextPrompt),
		},
	}

	// 如果有图片URL，添加到内容中
	if request.ImageURL != "" {
		imageItem := &model.CreateContentGenerationContentItem{
			Type: "image_url",
			ImageURL: &model.ImageURL{
				URL: request.ImageURL,
			},
		}
		content = append(content, imageItem)
	}

	// 构建API请求
	req := model.CreateContentGenerationTaskRequest{
		Model:   request.Model,
		Content: content,
	}

	// 调用API创建任务
	resp, err := m.client.CreateContentGenerationTask(ctx, req)
	if err != nil {
		return &CreateTaskResponse{Error: err.Error()}, err
	}

	return &CreateTaskResponse{
		ID: resp.ID,
	}, nil
}

// QueryTask 查询内容生成任务状态和结果
func (m *ContentGenerationTaskManager) QueryTask(ctx context.Context, taskID string) (*QueryTaskResponse, error) {
	if taskID == "" {
		return &QueryTaskResponse{Error: "任务ID不能为空"}, errors.New("任务ID不能为空")
	}

	// 构建查询请求
	req := model.GetContentGenerationTaskRequest{
		ID: taskID,
	}

	// 调用API查询任务
	resp, err := m.client.GetContentGenerationTask(ctx, req)
	if err != nil {
		return &QueryTaskResponse{Error: err.Error()}, err
	}

	// 构建响应数据，只返回状态和视频URL
	result := &QueryTaskResponse{
		Status: resp.Status,
	}

	// 如果任务成功，获取视频URL
	if resp.Status == TaskStatusSucceeded && resp.Content.VideoURL != "" {
		result.VideoURL = resp.Content.VideoURL
	}

	return result, nil
}
