package utils

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"time"
)

// DownloadFile downloads a file from a URL and saves it to a local file.
// If the local file path is empty, a temporary file is created with a name
// based on the MD5 hash of the URL and the original file extension.
func DownloadFile(rawURL, filePath string) (string, error) {

	if filePath == "" {
		u, err := url.Parse(rawURL)
		if err != nil {
			return "", err
		}
		ext := path.Ext(u.Path)
		if len(ext) > 10 || ext == "" {
			ext = ".tmp"
		}
		hash := md5.Sum([]byte(rawURL))
		fileName := hex.EncodeToString(hash[:]) + ext
		filePath = filepath.Join(os.TempDir(), fileName)
	}
	// Use an HTTP client with timeout to avoid hanging requests
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Get(rawURL)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	// Ensure we only proceed on successful HTTP responses (2xx)
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		// Read a small portion of body to help debug without large memory usage
		limited := io.LimitReader(resp.Body, 1024)
		b, _ := io.ReadAll(limited)
		return "", fmt.Errorf("download failed: status=%d url=%s body=%q", resp.StatusCode, rawURL, string(b))
	}
	outFile, err := os.Create(filePath)
	if err != nil {
		return "", err
	}
	defer outFile.Close()

	_, err = io.Copy(outFile, resp.Body)
	if err != nil {
		return "", err
	}

	return filePath, nil
}
