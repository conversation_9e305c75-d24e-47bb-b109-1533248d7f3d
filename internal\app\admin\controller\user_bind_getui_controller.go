package controller

import (
	"chongli/component"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

// UserBindGetuiController 用户个推绑定控制器
type UserBindGetuiController struct {
	log  *logger.Logger
	repo repo.UserBindGetuiRepo
}

// NewUserBindGetuiController 创建用户个推绑定控制器实例
func NewUserBindGetuiController(bootStrap *component.BootStrap, userBindGetuiRepo repo.UserBindGetuiRepo) *UserBindGetuiController {
	return &UserBindGetuiController{
		log:  bootStrap.Log,
		repo: userBindGetuiRepo,
	}
}

// AddUserBindGetui 添加用户个推绑定
func (c *UserBindGetuiController) AddUserBindGetui(ctx *gin.Context) {
	var req dto.UserBindGetuiDto
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, req, nil, errpkg.NewLowError("请求参数错误: "+err.Error()), response.WithNoSLSLog)
		return
	}

	result, err := c.repo.Add(&req)
	if err != nil {
		c.log.Error("添加用户个推绑定失败: %v", err)
		response.Response(ctx, req, nil, errpkg.NewHighError("添加失败"), response.WithSLSLog)
		return
	}

	response.Response(ctx, req, result, nil, response.WithSLSLog)
}

// GetOneUserBindGetui 获取单个用户个推绑定
func (c *UserBindGetuiController) GetOneUserBindGetui(ctx *gin.Context) {
	var req dto.UserBindGetuiDto
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, req, nil, errpkg.NewLowError("请求参数错误: "+err.Error()), response.WithNoSLSLog)
		return
	}

	result, err := c.repo.GetOne(&req)
	if err != nil {
		c.log.Error("获取单个用户个推绑定失败: %v", err)
		response.Response(ctx, req, nil, errpkg.NewHighError("获取失败"), response.WithSLSLog)
		return
	}

	if result == nil {
		response.Response(ctx, req, nil, errpkg.NewLowError("记录不存在"), response.WithNoSLSLog)
		return
	}

	response.Response(ctx, req, result, nil, response.WithSLSLog)
}

// SelectUserBindGetui 获取用户个推绑定列表
func (c *UserBindGetuiController) SelectUserBindGetui(ctx *gin.Context) {
	var req dto.UserBindGetuiDto
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, req, nil, errpkg.NewLowError("请求参数错误: "+err.Error()), response.WithNoSLSLog)
		return
	}

	list, err := c.repo.Select(&req) // total is now 0 from DAO
	if err != nil {
		c.log.Error("获取用户个推绑定列表失败: %v", err)
		response.Response(ctx, req, nil, errpkg.NewHighError("获取失败"), response.WithSLSLog)
		return
	}

	total, err := c.repo.Count(&req)
	if err != nil {
		c.log.Error("获取用户个推绑定总数失败: %v", err)
		response.Response(ctx, req, nil, errpkg.NewHighError("获取总数失败"), response.WithSLSLog)
		return
	}

	response.Response(ctx, req, gin.H{
		"list":     list,
		"total":    total,
		"page":     req.Page,
		"pageSize": req.PageSize,
	}, nil, response.WithSLSLog)
}

// UpdateUserBindGetui 更新用户个推绑定
func (c *UserBindGetuiController) UpdateUserBindGetui(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError("无效的ID"), response.WithNoSLSLog)
		return
	}

	var updates map[string]interface{}
	if err := ctx.ShouldBindJSON(&updates); err != nil {
		response.Response(ctx, updates, nil, errpkg.NewLowError("请求参数错误: "+err.Error()), response.WithNoSLSLog)
		return
	}

	result, err := c.repo.Update(id, updates)
	if err != nil {
		c.log.Error("更新用户个推绑定失败: %v", err)
		response.Response(ctx, updates, nil, errpkg.NewHighError("更新失败: "+err.Error()), response.WithSLSLog)
		return
	}

	response.Response(ctx, updates, result, nil, response.WithSLSLog)
}

// DeleteUserBindGetui 删除用户个推绑定
func (c *UserBindGetuiController) DeleteUserBindGetui(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError("无效的ID"), response.WithNoSLSLog)
		return
	}

	if err := c.repo.Delete(id); err != nil {
		c.log.Error("删除用户个推绑定失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError("删除失败"), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, nil, nil, response.WithSLSLog)
}
