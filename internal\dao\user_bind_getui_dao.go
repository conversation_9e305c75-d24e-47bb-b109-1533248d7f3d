package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

type userBindGetuiDao struct {
	db  *gorm.DB
	log *logger.Logger
}

func NewUserBindGetuiRepo(bootStrap *component.BootStrap) repo.UserBindGetuiRepo {
	return &userBindGetuiDao{
		db:  bootStrap.Driver.GetMysqlDb(),
		log: bootStrap.Log,
	}
}

// convertToModel converts a DTO to a model
func (d *userBindGetuiDao) convertToModel(dto *dto.UserBindGetuiDto) *model.UserBindGetui {
	if dto == nil {
		return nil
	}
	return &model.UserBindGetui{
		Id:       int64(dto.ID),
		UserId:   int64(dto.UserID),
		ClientId: dto.ClientID,
		// CreateAt and UpdateAt are handled by GORM autoCreateTime/autoUpdateTime
	}
}

// convertToDto converts a model to a DTO
func (d *userBindGetuiDao) convertToDto(m *model.UserBindGetui) *dto.UserBindGetuiDto {
	if m == nil {
		return nil
	}
	return &dto.UserBindGetuiDto{
		ID:        int(m.Id),
		UserID:    int(m.UserId),
		ClientID:  m.ClientId,
		CreatedAt: m.CreateAt,
		UpdatedAt: m.UpdateAt,
	}
}

// buildQueryCondition builds the query for select and getOne methods
func (d *userBindGetuiDao) buildQueryCondition(query *gorm.DB, queryDto *dto.UserBindGetuiDto) *gorm.DB {
	if queryDto.ID > 0 {
		query = query.Where("id = ?", queryDto.ID)
	}
	if queryDto.UserID > 0 {
		query = query.Where("user_id = ?", queryDto.UserID)
	}
	if queryDto.ClientID != "" {
		query = query.Where("client_id = ?", queryDto.ClientID)
	}
	// Assuming IsDelete is a field in the model and DTO for filtering
	// if queryDto.IsDelete != 0 {
	// 	query = query.Where("is_delete = ?", queryDto.IsDelete)
	// }
	return query
}

// GetOne retrieves a single record based on query parameters
func (d *userBindGetuiDao) GetOne(queryDto *dto.UserBindGetuiDto) (*dto.UserBindGetuiDto, error) {
	var m model.UserBindGetui
	queryBuilder := d.db.Model(&model.UserBindGetui{})
	queryBuilder = d.buildQueryCondition(queryBuilder, queryDto)

	err := queryBuilder.First(&m).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // Not found is not an error
		}
		logger.Log().Error("GetOne user bind getui failed: %v", err)
		return nil, err
	}
	return d.convertToDto(&m), nil
}

// Count retrieves the total number of records based on query parameters
func (d *userBindGetuiDao) Count(queryDto *dto.UserBindGetuiDto) (int64, error) {
	var total int64
	queryBuilder := d.db.Model(&model.UserBindGetui{})
	queryBuilder = d.buildQueryCondition(queryBuilder, queryDto)

	err := queryBuilder.Count(&total).Error
	if err != nil {
		logger.Log().Error("Count user bind getui failed: %v", err)
		return 0, err
	}
	return total, nil
}

// Select retrieves multiple records with pagination and sorting
func (d *userBindGetuiDao) Select(queryDto *dto.UserBindGetuiDto) ([]*dto.UserBindGetuiDto, error) {
	var models []*model.UserBindGetui

	queryBuilder := d.db.Model(&model.UserBindGetui{})
	queryBuilder = d.buildQueryCondition(queryBuilder, queryDto)

	// Apply pagination
	if queryDto.Page <= 0 {
		queryDto.Page = 1
	}
	if queryDto.PageSize <= 0 {
		queryDto.PageSize = 10
	}
	offset := (queryDto.Page - 1) * queryDto.PageSize
	queryBuilder = queryBuilder.Offset(offset).Limit(queryDto.PageSize)

	// Apply sorting
	if queryDto.OrderBy != "" && queryDto.Sort != "" {
		queryBuilder = queryBuilder.Order(fmt.Sprintf("%s %s", queryDto.OrderBy, queryDto.Sort))
	} else {
		queryBuilder = queryBuilder.Order("id DESC") // Default sort
	}

	err := queryBuilder.Find(&models).Error
	if err != nil {
		logger.Log().Error("Select user bind getui failed: %v", err)
		return nil, err
	}

	var dtos []*dto.UserBindGetuiDto
	for _, m := range models {
		dtos = append(dtos, d.convertToDto(m))
	}

	// The total count will be fetched by the separate Count method
	return dtos, nil // Return 0 for total here, as it's now handled by Count
}

// Add creates a new record
func (d *userBindGetuiDao) Add(dataDto *dto.UserBindGetuiDto) (*dto.UserBindGetuiDto, error) {
	m := d.convertToModel(dataDto)
	err := d.db.Create(m).Error
	if err != nil {
		logger.Log().Error("Add user bind getui failed: %v", err)
		return nil, err
	}
	return d.convertToDto(m), nil
}

// Update updates a record by ID with a map of fields
func (d *userBindGetuiDao) Update(id int, updates map[string]interface{}) (*dto.UserBindGetuiDto, error) {
	var m model.UserBindGetui
	// Check if record exists before updating
	err := d.db.Where("id = ?", id).First(&m).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("record with ID %d not found", id)
		}
		logger.Log().Error("Failed to find record for update: %v", err)
		return nil, err
	}

	err = d.db.Model(&m).Updates(updates).Error
	if err != nil {
		logger.Log().Error("Update user bind getui failed: %v", err)
		return nil, err
	}
	// Retrieve the updated record to return the full DTO
	err = d.db.Where("id = ?", id).First(&m).Error
	if err != nil {
		logger.Log().Error("Failed to retrieve updated user bind getui: %v", err)
		return nil, err
	}
	return d.convertToDto(&m), nil
}

// Delete deletes a record by ID (hard delete)
func (d *userBindGetuiDao) Delete(id int) error {
	// Check if the model has an IsDelete field for soft delete
	// If it does, you might want to update that field instead of hard deleting.
	// For this implementation, we are performing a hard delete.
	err := d.db.Where("id = ?", id).Delete(&model.UserBindGetui{}).Error
	if err != nil {
		logger.Log().Error("Delete user bind getui failed: %v", err)
		return err
	}
	return nil
}

// Existing methods, updated to use new DTO for consistency
func (d *userBindGetuiDao) GetBindInfoByUserId(userId int64) (*dto.UserBindGetuiDto, error) {
	var m model.UserBindGetui
	if err := d.db.Model(&model.UserBindGetui{}).Where("user_id = ?", userId).First(&m).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		logger.Log().Error("获取用户cid异常: %v", err.Error())
		return nil, err
	}
	return d.convertToDto(&m), nil
}

func (d *userBindGetuiDao) GetBindInfoByCid(cid string) (*dto.UserBindGetuiDto, error) {
	var m model.UserBindGetui
	if err := d.db.Model(&model.UserBindGetui{}).Where("client_id = ?", cid).First(&m).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		logger.Log().Error("获取用户cid异常: %v", err.Error())
		return nil, err
	}
	return d.convertToDto(&m), nil
}

func (d *userBindGetuiDao) DeleteBindByUserId(userId int64) error {
	if err := d.db.Where("user_id = ?", userId).Delete(&model.UserBindGetui{}).Error; err != nil {
		logger.Log().Error("删除用户cid异常: %v", err.Error())
		return err
	}
	return nil
}

func (d *userBindGetuiDao) BindUserIdAndCid(userId int64, cid string) error {
	m := &model.UserBindGetui{
		UserId:   userId,
		ClientId: cid,
	}
	if err := d.db.Create(m).Error; err != nil {
		logger.Log().Error("绑定用户cid异常: %v", err.Error())
		return err
	}
	return nil
}

func (d *userBindGetuiDao) UpdateBind(userId int64, cid string) error {
	if err := d.db.Model(&model.UserBindGetui{}).Where("user_id = ?", userId).Update("client_id", cid).Error; err != nil {
		logger.Log().Error("更新用户cid异常: %v", err.Error())
		return err
	}
	return nil
}

func (d *userBindGetuiDao) Page(req *dto.GetuiListRequest) ([]*dto.UserBindGetuiInfoDto, int64, error) {
	var users []*model.UserBindGetui
	var total int64
	query := d.db.Model(&model.UserBindGetui{})

	// 构建查询条件
	if req.UserId != 0 {
		query = query.Where("user_id = ?", req.UserId)
	}

	if req.Cid != "" {
		query = query.Where("client_id = ?", req.Cid)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		d.log.Error("查询总数失败: %v", err)
		return nil, 0, err
	}

	// 获取列表
	offset := (req.Page - 1) * req.Size
	err = query.Offset(offset).Limit(req.Size).Order("create_at DESC").Find(&users).Error
	if err != nil {
		d.log.Error("查询列表失败: %v", err)
		return nil, 0, err
	}

	// 转换为DTO
	var dtoList []*dto.UserBindGetuiInfoDto
	for _, user := range users {
		dtoList = append(dtoList, &dto.UserBindGetuiInfoDto{
			Id:       user.Id,
			UserId:   user.UserId,
			Cid:      user.ClientId,
			CreateAt: user.CreateAt,
			UpdateAt: user.UpdateAt,
		})
	}

	return dtoList, total, nil
}
