package hidden_pic_watermark

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"testing"
)

func TestReadWatermark(t *testing.T) {
	// Test PNG
	pngFilePath := "/mnt/c/Users/<USER>/Downloads/cat.jpg"
	pngWatermark := "chongli"

	url := "https://chongli-cdn.51wnl-cq.com/chongli-ai-pic/2025/08/19/d992c0718708858158f1751abfb12701.jpg"
	// 先下载 URL 到 pngFilePath
	resp, err := http.Get(url)
	if err != nil {
		t.Fatalf("download failed: %v", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		t.Fatalf("download bad status: %s", resp.Status)
	}
	f, err := os.Create(pngFilePath)
	if err != nil {
		t.Fatalf("create file failed: %v", err)
	}
	if _, err := io.Copy(f, resp.Body); err != nil {
		f.Close()
		t.Fatalf("write file failed: %v", err)
	}
	if err := f.Close(); err != nil {
		t.Fatalf("close file failed: %v", err)
	}

	readPngWatermark, err := ReadWatermark(pngFilePath)
	if err != nil {
		t.Fatalf("Failed to read watermark from png: %v", err)
	}

	if readPngWatermark != pngWatermark {
		t.Errorf("Expected watermark '%s' for png, but got '%s'", pngWatermark, readPngWatermark)
	}
	fmt.Println("readPngWatermark: ", readPngWatermark)

	// 美化的成功输出
	fmt.Printf("\n================ Image Watermark Verification ================\n")
	fmt.Printf("✅ Success\n")
	fmt.Printf("- Source URL : %s\n", url)
	fmt.Printf("- Save Path  : %s\n", pngFilePath)
	fmt.Printf("- Watermark  : %q\n", readPngWatermark)
	fmt.Printf("============================================================\n\n")

}
