package hidden_pic_watermark

import (
	"bufio"
	"bytes"
	"encoding/binary"
	"errors"
	"hash/crc32"
	"io"
	"os"
)

const (
	pngSignature    = "\x89PNG\r\n\x1a\n"
	customChunkType = "hiDn"
	jpegSOIMarker   = 0xFFD8
	jpegEOIMarker   = 0xFFD9
	jpegApp15Marker = 0xFFEF
)

// AddWatermark adds a hidden watermark to a file (PNG or JPEG).
func AddWatermark(filePath string, watermark string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	buffer := make([]byte, 8)
	if _, err := io.ReadFull(file, buffer); err != nil {
		return err
	}

	if _, err := file.Seek(0, 0); err != nil {
		return err
	}

	if string(buffer) == pngSignature {
		// PNG logic
		newFile, err := os.Create(filePath + ".tmp")
		if err != nil {
			return err
		}
		defer newFile.Close()

		// Copy the first 33 bytes (signature + IHDR chunk)
		if _, err := io.CopyN(newFile, file, 33); err != nil {
			return err
		}

		// Create and write the custom chunk
		chunkData := []byte(watermark)
		chunkLength := uint32(len(chunkData))
		chunkType := []byte(customChunkType)
		chunkContent := new(bytes.Buffer)
		chunkContent.Write(chunkType)
		chunkContent.Write(chunkData)
		crc := crc32.NewIEEE()
		crc.Write(chunkContent.Bytes())
		crcSum := crc.Sum32()

		if err := binary.Write(newFile, binary.BigEndian, chunkLength); err != nil {
			return err
		}
		if _, err := newFile.Write(chunkContent.Bytes()); err != nil {
			return err
		}
		if err := binary.Write(newFile, binary.BigEndian, crcSum); err != nil {
			return err
		}

		// Copy the rest of the file
		if _, err := io.Copy(newFile, file); err != nil {
			return err
		}
		file.Close()
		newFile.Close()
		return os.Rename(filePath+".tmp", filePath)

	} else if buffer[0] == 0xFF && buffer[1] == 0xD8 {
		// JPEG logic
		newFile, err := os.Create(filePath + ".tmp")
		if err != nil {
			return err
		}
		defer newFile.Close()

		// Write SOI
		if _, err := newFile.Write(buffer[:2]); err != nil {
			return err
		}

		// Create and write APP15 segment
		watermarkData := []byte(watermark)
		segmentLength := uint16(len(watermarkData) + 2)

		if err := binary.Write(newFile, binary.BigEndian, uint16(jpegApp15Marker)); err != nil {
			return err
		}
		if err := binary.Write(newFile, binary.BigEndian, segmentLength); err != nil {
			return err
		}
		if _, err := newFile.Write(watermarkData); err != nil {
			return err
		}

		// Copy the rest of the file
		if _, err := file.Seek(2, 0); err != nil {
			return err
		}
		if _, err := io.Copy(newFile, file); err != nil {
			return err
		}
		file.Close()
		newFile.Close()
		return os.Rename(filePath+".tmp", filePath)
	}

	return errors.New("unsupported file type")
}

// ReadWatermark reads a hidden watermark from a file (PNG or JPEG).
func ReadWatermark(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	buffer := make([]byte, 8)
	if _, err := io.ReadFull(file, buffer); err != nil {
		return "", err
	}

	if string(buffer) == pngSignature {
		// PNG logic
		if _, err := file.Seek(8, 0); err != nil {
			return "", err
		}
		for {
			var length uint32
			if err := binary.Read(file, binary.BigEndian, &length); err != nil {
				if err == io.EOF {
					break
				}
				return "", err
			}
			chunkTypeBytes := make([]byte, 4)
			if _, err := io.ReadFull(file, chunkTypeBytes); err != nil {
				return "", err
			}
			if string(chunkTypeBytes) == customChunkType {
				chunkData := make([]byte, length)
				if _, err := io.ReadFull(file, chunkData); err != nil {
					return "", err
				}
				// Skip CRC
				if _, err := file.Seek(4, io.SeekCurrent); err != nil {
					return "", err
				}
				return string(chunkData), nil
			}
			if _, err := file.Seek(int64(length+4), io.SeekCurrent); err != nil {
				return "", err
			}
		}
	} else if buffer[0] == 0xFF && buffer[1] == 0xD8 {
		// JPEG logic
		if _, err := file.Seek(2, 0); err != nil {
			return "", err
		}
		reader := bufio.NewReader(file)
		for {
			var marker uint16
			if err := binary.Read(reader, binary.BigEndian, &marker); err != nil {
				if err == io.EOF {
					break
				}
				return "", err
			}
			if marker == jpegEOIMarker {
				break
			}
			var length uint16
			if err := binary.Read(reader, binary.BigEndian, &length); err != nil {
				return "", err
			}
			if marker == jpegApp15Marker {
				data := make([]byte, length-2)
				if _, err := io.ReadFull(reader, data); err != nil {
					return "", err
				}
				return string(data), nil
			}
			if length > 2 {
				if _, err := reader.Discard(int(length - 2)); err != nil {
					return "", err
				}
			}
		}
	}

	return "", errors.New("watermark not found")
}
