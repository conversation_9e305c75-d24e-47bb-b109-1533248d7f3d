package dto

import "time"

// UserReceiveDto 用户领取记录数据传输对象
type UserReceiveDto struct {
	ID         int64     `json:"id"`          // 主键id
	UserID     int64     `json:"user_id"`     // 用户id
	DeviceID   string    `json:"device_id"`   // 设备id
	ReceiveNum int       `json:"receive_num"` // 领取数量
	ReceiveAt  time.Time `json:"receive_at"`  // 领取时间
	Version    string    `json:"version"`     // APP版本
	Channel    string    `json:"channel"`     // 设备渠道
	CreateAt   time.Time `json:"create_at"`   // 创建时间
}

// CreateUserReceiveRequest 创建用户领取记录请求
type CreateUserReceiveRequest struct {
	UserID     int64  `json:"user_id" binding:"required"`     // 用户id
	DeviceID   string `json:"device_id" binding:"required"`   // 设备id
	ReceiveNum int    `json:"receive_num" binding:"required"` // 领取数量
	Version    string `json:"version" binding:"required"`     // APP版本
	Channel    string `json:"channel" binding:"required"`     // 设备渠道
}

// UpdateUserReceiveRequest 更新用户领取记录请求
type UpdateUserReceiveRequest struct {
	ID         int64  `json:"id" binding:"required"` // 主键id
	UserID     int64  `json:"user_id"`               // 用户id
	DeviceID   string `json:"device_id"`             // 设备id
	ReceiveNum int    `json:"receive_num"`           // 领取数量
	Version    string `json:"version"`               // APP版本
	Channel    string `json:"channel"`               // 设备渠道
}

// UserReceivePageRequest 用户领取记录分页查询请求
type UserReceivePageRequest struct {
	Page     int       `json:"page" form:"page" binding:"min=1"`           // 页码
	PageSize int       `json:"page_size" form:"page_size" binding:"min=1"` // 每页大小
	UserID   int64     `json:"user_id" form:"user_id"`                     // 用户id
	DeviceID string    `json:"device_id" form:"device_id"`                 // 设备id（模糊查询）
	Version  string    `json:"version" form:"version"`                     // APP版本
	Channel  string    `json:"channel" form:"channel"`                     // 设备渠道
	BeginAt  time.Time `json:"begin_at" form:"begin_at"`                   // 开始领取时间
	EndAt    time.Time `json:"end_at" form:"end_at"`                       // 结束领取时间
}

// UserReceivePageResponse 用户领取记录分页查询响应
type UserReceivePageResponse struct {
	Total int64             `json:"total"` // 总数
	List  []*UserReceiveDto `json:"list"`  // 列表
}

// BatchDeleteUserReceiveRequest 批量删除用户领取记录请求
type BatchDeleteUserReceiveRequest struct {
	IDs []int64 `json:"ids" binding:"required,min=1"` // 用户领取记录ID列表
}
