package main

import (
	"chongli/component"
	"chongli/pkg/aliyun"
	"testing"
)

func TestImageCheck(t *testing.T) {
	bootstrap := component.NewBootStrap()
	aliClient := aliyun.NewAliGreenClient(bootstrap)

	// 测试正常图片
	//result, err := aliClient.CheckImage(`http://chongli-cdn.51wnl-cq.com/chongli/2025/08/15/6c388c8b08f6b17d926effaf4accc6fd.jpg`)
	//if err != nil {
	//	t.Error(err)
	//	return
	//}
	//if result != "" {
	//	t.Error(result)
	//	return
	//}

	// 测试异常图片
	result, err := aliClient.CheckImage(`http://chongli-cdn.51wnl-cq.com/chongli/2025/08/15/6c388c8b08f6b17d926effaf4accc6fdaa.jpg`)
	if err != nil {
		t.Error(err)
		return
	}
	if result == "" {
		t.Error("异常图片检测失败")
		return
	} else {
		t.Log(result)
	}
}
