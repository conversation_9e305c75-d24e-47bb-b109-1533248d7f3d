package dto

import (
	"time"
)

// TemplateAIDTO AI模板数据传输对象
type TemplateAIDTO struct {
	ID              uint64    `json:"id" form:"id"`
	Name            string    `json:"name" form:"name"`
	CoverURL        string    `json:"cover_url" form:"cover_url"`
	VideoCoverURL   string    `json:"video_cover_url" form:"video_cover_url"`
	VideoFirstFrame string    `json:"video_first_frame" form:"video_first_frame"` // 视频首帧封面
	Status          int8      `json:"status" form:"status"`
	SortOrder       int       `json:"sort_order" form:"sort_order"`
	MaxVersionInt   int64     `json:"max_version_int,omitempty" form:"max_version_int"` // 最大适用版本int值
	MaxVersion      string    `json:"max_version" form:"max_version"`                   // 最大适用版本
	CategoryID      uint64    `json:"category_id" form:"category_id"`
	MainClass       int       `json:"main_class" form:"main_class"` // 主分类 1-写真 2-唱歌 3-跳舞
	VariablesJSON   JSONMap   `json:"variables_json" form:"variables_json"`
	CreateAt        time.Time `json:"create_at" form:"create_at"`
	UpdatedAt       time.Time `json:"update_at"`
	DiamondCost     int       `json:"diamond_cost" form:"diamond_cost"` // 钻石花费
	MakeType        string    `json:"make_type" form:"make_type"`       // 模板制作类型，单图/多图等，值为字符串枚举，比如 “single_image”
	Category        TemplateCategoryDto

	// 视频分辨率
	VideoHigh  int64 `json:"video_high,omitempty"`
	VideoWidth int64 `json:"video_width,omitempty"`
	// 图片分辨率
	ImageHigh  int64 `json:"image_high,omitempty"`
	ImageWidth int64 `json:"image_width,omitempty"`
}

type TemplateDto struct {
	ID              uint64               `json:"id" form:"id"`
	Name            string               `json:"name" form:"name"`
	CoverURL        string               `json:"cover_url" form:"cover_url"`
	VideoCoverURL   string               `json:"video_cover_url" form:"video_cover_url"`
	VideoFirstFrame string               `json:"video_first_frame" form:"video_first_frame"` // 视频首帧封面
	Status          int8                 `json:"status" form:"status"`
	SortOrder       int                  `json:"sort_order" form:"sort_order"`
	MaxVersion      string               `json:"max_version"`
	MaxVersionInt   int64                `json:"max_version_int,omitempty"` // 最大适用版本int值
	CategoryID      uint64               `json:"category_id"`
	MainClass       int                  `json:"main_class"` // 主分类 1-写真 2-唱歌 3-跳舞
	CreateAt        time.Time            `json:"create_at"`
	UpdatedAt       time.Time            `json:"update_at"`
	DiamondCost     int                  `json:"diamond_cost"` // 钻石花费
	MakeType        string               `json:"make_type"`    // 模板制作类型，单图/多图等，值为字符串枚举，比如 “single_image”
	Category        *TemplateCategoryDto `json:"category,omitempty"`

	// 视频分辨率
	VideoHigh  int64 `json:"video_high,omitempty"`
	VideoWidth int64 `json:"video_width,omitempty"`
	// 图片分辨率
	ImageHigh  int64 `json:"image_high,omitempty"`
	ImageWidth int64 `json:"image_width,omitempty"`
}

type AIPicTemplate struct {
	// 宠物描述
	PetDesc string `json:"pet_desc"`
	// 背景描述
	BackgroundDesc string `json:"background_desc"`
	// 构图描述
	CompositionDesc string `json:"composition_desc"`
	// 风格选择
	Style string `json:"style"`
	// 风格描述
	StyleDesc string `json:"style_desc"`
	// 模型强度
	ModelStrength float64 `json:"strength"`

	VideoDesc string `json:"video_desc"`
	// 音频url
	AudioUrl string `json:"audio_url"`
	//
	WorkflowUrl string `json:"workflow_url"`
	// 视频时长
	VideoLength int `json:"video_length"`
}

// TemplateAIQueryDTO 查询AI模板的参数
type TemplateAIQueryDTO struct {
	Template TemplateAIDTO

	Category TemplateCategoryDto

	// 主分类 1-写真 2-唱歌 3-跳舞
	MainClass int `json:"main_class" form:"main_class"`

	// 分页参数
	PageNum  int `json:"page_num" form:"page_num"`
	PageSize int `json:"page_size" form:"page_size"`

	// 排序参数
	OrderBy        string `json:"order_by,omitempty"`        // 排序字段
	OrderDirection string `json:"order_direction,omitempty"` // 排序方向：asc/desc

	// 统计标签参数，用于根据统计数据排序
	// 支持的值：today_view, today_make, yesterday_view, yesterday_make, week_view, week_make, month_view, month_make, total_view, total_make
	StatsTag string `json:"stats_tag,omitempty" form:"stats_tag"`
}
