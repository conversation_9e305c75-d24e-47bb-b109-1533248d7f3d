package repo

import (
	"chongli/internal/service/dto"
	"gorm.io/gorm"
)

// UserReceiveRepo 用户领取记录数据仓库接口
type UserReceiveRepo interface {
	// CreateUserReceive 创建用户领取记录
	CreateUserReceive(req *dto.CreateUserReceiveRequest, tx ...*gorm.DB) (*dto.UserReceiveDto, error)
	// GetUserReceiveById 根据ID查询用户领取记录
	GetUserReceiveById(id int64, tx ...*gorm.DB) (*dto.UserReceiveDto, error)
	// UpdateUserReceive 更新用户领取记录
	UpdateUserReceive(req *dto.UpdateUserReceiveRequest, tx ...*gorm.DB) error
	// DeleteUserReceive 删除用户领取记录（物理删除）
	DeleteUserReceive(id int64, tx ...*gorm.DB) error
	// GetUserReceiveByUserId 根据用户ID查询用户领取记录
	GetUserReceiveByUserId(userId int64, tx ...*gorm.DB) ([]*dto.UserReceiveDto, error)
	// GetUserReceiveByDeviceId 根据设备ID查询用户领取记录
	GetUserReceiveByDeviceId(deviceId string, tx ...*gorm.DB) ([]*dto.UserReceiveDto, error)
	// PageUserReceive 分页查询用户领取记录
	PageUserReceive(req *dto.UserReceivePageRequest) (*dto.UserReceivePageResponse, error)
	// ListUserReceive 列表查询用户领取记录
	ListUserReceive(userId int64, deviceId string, tx ...*gorm.DB) ([]*dto.UserReceiveDto, error)
	// BatchDeleteUserReceive 批量删除用户领取记录
	BatchDeleteUserReceive(ids []int64, tx ...*gorm.DB) error
	// GetUserReceiveCount 获取用户领取记录总数
	GetUserReceiveCount(userId int64, deviceId string, tx ...*gorm.DB) (int64, error)
}
